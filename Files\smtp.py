import smtplib
import ssl
from email.mime.text import MIME<PERSON>ext
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.utils import formataddr
from email.header import Header
import os, sys, ctypes, logging, json, time, threading, queue, urllib.request, socket
try:
    import socks
except ImportError:
    socks = None
from time import sleep
from functools import partial
from PyQt6.QtWidgets import QA<PERSON>lication,QDialog,QVBoxLayout,QHBoxLayout,QMainWindow, QPlainTextEdit, QVBoxLayout, QPushButton, QMessageBox
from PyQt6.QtWebEngineWidgets import QWebEngineView
from PyQt6.QtWebEngineCore import QWebEngineSettings
from PyQt6.QtCore import QUrl,Qt, QObject, pyqtSignal, QThread, QTimer
from PyQt6 import uic, QtCore
from PyQt6.QtGui import QIcon


myappid = 'mycompany.myproduct.subproduct.version'
ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(myappid)
home = os.path.dirname(os.path.realpath(__file__)).replace("\\", "/")


class ProxyManager:
    """
    A class to manage HTTP/SOCKS proxy connections for email sending.
    This class provides methods to configure, rotate, and manage proxy connections.
    """

    def __init__(self, emails_per_proxy=5):
        """
        Initialize the proxy manager.

        Args:
            emails_per_proxy (int): Number of emails to send before rotating to next proxy
        """
        self.emails_per_proxy = emails_per_proxy
        self.proxies = []
        self.current_proxy_index = 0
        self.emails_sent_with_current_proxy = 0
        self.proxy_lock = threading.Lock()
        self.logger = logging.getLogger("ProxyManager")

        # Set up logging
        self.logger.setLevel(logging.DEBUG)
        if not self.logger.handlers:
            handler = logging.FileHandler(f"{home}/proxy_manager.log")
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)

    def load_proxies_from_file(self, filename):
        """
        Load proxy configurations from a file.

        Expected format: protocol://host:port:username:password (one per line)
        Example: http://proxy.example.com:8080:user:pass
                socks5://proxy.example.com:1080:user:pass

        Args:
            filename (str): Path to the proxy configuration file
        """
        try:
            self.proxies = []
            with open(filename, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        proxy_config = self.parse_proxy_string(line)
                        if proxy_config:
                            self.proxies.append(proxy_config)

            self.logger.info(f"Loaded {len(self.proxies)} proxies from {filename}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to load proxies from {filename}: {str(e)}")
            return False

    def add_proxy(self, proxy_string):
        """
        Add a single proxy to the proxy list.

        Args:
            proxy_string (str): Proxy string in format protocol://host:port:username:password

        Returns:
            bool: True if proxy was added successfully
        """
        proxy_config = self.parse_proxy_string(proxy_string)
        if proxy_config:
            self.proxies.append(proxy_config)
            self.logger.info(f"Added proxy: {proxy_config['host']}:{proxy_config['port']}")
            return True
        return False

    def parse_proxy_string(self, proxy_string):
        """
        Parse a proxy string into a configuration dictionary.

        Args:
            proxy_string (str): Proxy string in format protocol://host:port:username:password

        Returns:
            dict: Proxy configuration or None if invalid
        """
        try:
            # Split protocol from the rest
            if '://' not in proxy_string:
                return None

            protocol, rest = proxy_string.split('://', 1)
            protocol = protocol.lower()

            if protocol not in ['http', 'https', 'socks4', 'socks5']:
                return None

            # Parse host:port:username:password
            parts = rest.split(':')
            if len(parts) < 2:
                return None

            host = parts[0]
            port = int(parts[1])
            username = parts[2] if len(parts) > 2 else None
            password = parts[3] if len(parts) > 3 else None

            return {
                'protocol': protocol,
                'host': host,
                'port': port,
                'username': username,
                'password': password
            }
        except Exception as e:
            self.logger.error(f"Failed to parse proxy string '{proxy_string}': {str(e)}")
            return None

    def get_current_proxy(self):
        """
        Get the current proxy configuration.

        Returns:
            dict: Current proxy configuration or None if no proxies available
        """
        with self.proxy_lock:
            if not self.proxies:
                return None
            return self.proxies[self.current_proxy_index]

    def rotate_proxy(self):
        """
        Rotate to the next proxy in the list.

        Returns:
            dict: New current proxy configuration or None if no proxies available
        """
        with self.proxy_lock:
            if not self.proxies:
                return None

            self.current_proxy_index = (self.current_proxy_index + 1) % len(self.proxies)
            self.emails_sent_with_current_proxy = 0

            new_proxy = self.proxies[self.current_proxy_index]
            self.logger.info(f"Rotated to proxy {self.current_proxy_index + 1}/{len(self.proxies)}: {new_proxy['host']}:{new_proxy['port']}")
            return new_proxy

    def should_rotate_proxy(self):
        """
        Check if it's time to rotate to the next proxy.

        Returns:
            bool: True if proxy should be rotated
        """
        with self.proxy_lock:
            return self.emails_sent_with_current_proxy >= self.emails_per_proxy

    def increment_email_count(self):
        """
        Increment the count of emails sent with the current proxy.
        """
        with self.proxy_lock:
            self.emails_sent_with_current_proxy += 1

    def configure_smtp_with_proxy(self, smtp_host, smtp_port, proxy_config=None):
        """
        Configure SMTP connection to use a proxy.

        Args:
            smtp_host (str): SMTP server hostname
            smtp_port (int): SMTP server port
            proxy_config (dict): Proxy configuration (uses current proxy if None)

        Returns:
            smtplib.SMTP: Configured SMTP object or None if failed
        """
        if proxy_config is None:
            proxy_config = self.get_current_proxy()

        if not proxy_config:
            # No proxy configured, use direct connection
            try:
                return smtplib.SMTP(smtp_host, smtp_port)
            except Exception as e:
                self.logger.error(f"Failed to create direct SMTP connection: {str(e)}")
                return None

        try:
            # Configure proxy based on protocol
            if proxy_config['protocol'] in ['socks4', 'socks5']:
                if socks is None:
                    self.logger.error("PySocks library not available for SOCKS proxy support")
                    return None

                # Create a socket with SOCKS proxy
                sock = socks.socksocket()

                if proxy_config['protocol'] == 'socks4':
                    proxy_type = socks.SOCKS4
                else:
                    proxy_type = socks.SOCKS5

                sock.set_proxy(
                    proxy_type,
                    proxy_config['host'],
                    proxy_config['port'],
                    username=proxy_config['username'],
                    password=proxy_config['password']
                )

                # Connect through the proxy
                sock.connect((smtp_host, smtp_port))

                # Create SMTP object with the proxied socket
                smtp = smtplib.SMTP()
                smtp.sock = sock
                smtp.file = sock.makefile('rb')

                return smtp

            elif proxy_config['protocol'] in ['http', 'https']:
                # For HTTP proxies, we'll need to use a different approach
                # This is more complex and may require additional libraries
                self.logger.warning("HTTP proxy support for SMTP is limited")
                # Fall back to direct connection for now
                return smtplib.SMTP(smtp_host, smtp_port)

        except Exception as e:
            self.logger.error(f"Failed to configure SMTP with proxy {proxy_config['host']}:{proxy_config['port']}: {str(e)}")
            return None

    def test_proxy_connection(self, proxy_config):
        """
        Test if a proxy is working by making a simple connection.

        Args:
            proxy_config (dict): Proxy configuration to test

        Returns:
            bool: True if proxy is working, False otherwise
        """
        try:
            if proxy_config['protocol'] in ['socks4', 'socks5']:
                if socks is None:
                    return False

                sock = socks.socksocket()

                if proxy_config['protocol'] == 'socks4':
                    proxy_type = socks.SOCKS4
                else:
                    proxy_type = socks.SOCKS5

                sock.set_proxy(
                    proxy_type,
                    proxy_config['host'],
                    proxy_config['port'],
                    username=proxy_config['username'],
                    password=proxy_config['password']
                )

                # Test connection to a known server
                sock.settimeout(10)
                sock.connect(('google.com', 80))
                sock.close()
                return True

            elif proxy_config['protocol'] in ['http', 'https']:
                # Test HTTP proxy by making a request
                proxy_url = f"{proxy_config['protocol']}://"
                if proxy_config['username'] and proxy_config['password']:
                    proxy_url += f"{proxy_config['username']}:{proxy_config['password']}@"
                proxy_url += f"{proxy_config['host']}:{proxy_config['port']}"

                proxy_handler = urllib.request.ProxyHandler({
                    'http': proxy_url,
                    'https': proxy_url
                })
                opener = urllib.request.build_opener(proxy_handler)

                # Test with a simple request
                request = urllib.request.Request('http://httpbin.org/ip')
                response = opener.open(request, timeout=10)
                return response.status == 200

        except Exception as e:
            self.logger.debug(f"Proxy test failed for {proxy_config['host']}:{proxy_config['port']}: {str(e)}")
            return False

    def get_working_proxies(self):
        """
        Test all configured proxies and return a list of working ones.

        Returns:
            list: List of working proxy configurations
        """
        working_proxies = []
        for proxy in self.proxies:
            if self.test_proxy_connection(proxy):
                working_proxies.append(proxy)
                self.logger.info(f"Proxy {proxy['host']}:{proxy['port']} is working")
            else:
                self.logger.warning(f"Proxy {proxy['host']}:{proxy['port']} is not working")

        return working_proxies


class MultiThreadedEmailSender:
    """
    A multi-threaded email sender that works with proxy rotation.
    """

    def __init__(self, num_threads=3, proxy_manager=None):
        """
        Initialize the multi-threaded email sender.

        Args:
            num_threads (int): Number of worker threads to use
            proxy_manager (ProxyManager): Proxy manager instance
        """
        self.num_threads = num_threads
        self.proxy_manager = proxy_manager
        self.email_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.threads = []
        self.stop_event = threading.Event()
        self.logger = logging.getLogger("MultiThreadedEmailSender")

        # Statistics
        self.emails_sent = 0
        self.emails_failed = 0
        self.stats_lock = threading.Lock()

    def add_email_to_queue(self, email_data):
        """
        Add an email to the sending queue.

        Args:
            email_data (dict): Email data containing recipient, sender info, etc.
        """
        self.email_queue.put(email_data)

    def worker_thread(self, thread_id):
        """
        Worker thread function for sending emails.

        Args:
            thread_id (int): Unique identifier for this thread
        """
        self.logger.info(f"Worker thread {thread_id} started")

        while not self.stop_event.is_set():
            try:
                # Get email from queue with timeout
                email_data = self.email_queue.get(timeout=1)

                # Send the email
                success = self.send_single_email(email_data, thread_id)

                # Update statistics
                with self.stats_lock:
                    if success:
                        self.emails_sent += 1
                    else:
                        self.emails_failed += 1

                # Put result in result queue
                self.result_queue.put({
                    'thread_id': thread_id,
                    'email_data': email_data,
                    'success': success,
                    'timestamp': time.time()
                })

                # Mark task as done
                self.email_queue.task_done()

            except queue.Empty:
                # No email in queue, continue checking
                continue
            except Exception as e:
                self.logger.error(f"Worker thread {thread_id} error: {str(e)}")

        self.logger.info(f"Worker thread {thread_id} stopped")

    def send_single_email(self, email_data, thread_id):
        """
        Send a single email using the configured proxy.

        Args:
            email_data (dict): Email data
            thread_id (int): Thread identifier

        Returns:
            bool: True if email was sent successfully
        """
        try:
            # Check if we should rotate proxy
            if self.proxy_manager and self.proxy_manager.should_rotate_proxy():
                self.proxy_manager.rotate_proxy()

            # Get SMTP configuration
            smtp_host = email_data.get('smtp_host', 'smtp.gmail.com')
            smtp_port = email_data.get('smtp_port', 587)

            # Create SMTP connection with proxy
            if self.proxy_manager:
                smtp = self.proxy_manager.configure_smtp_with_proxy(smtp_host, smtp_port)
            else:
                smtp = smtplib.SMTP(smtp_host, smtp_port)

            if not smtp:
                self.logger.error(f"Thread {thread_id}: Failed to create SMTP connection")
                return False

            # Configure SMTP
            smtp.starttls()
            smtp.login(email_data['sender_email'], email_data['sender_password'])

            # Create message
            msg = MIMEMultipart()
            msg['From'] = formataddr((email_data.get('from_name', ''), email_data['sender_email']))
            msg['To'] = email_data['recipient']
            msg['Subject'] = Header(email_data['subject'], 'utf-8')

            # Add body
            if email_data.get('html_content'):
                msg.attach(MIMEText(email_data['html_content'], 'html', 'utf-8'))
            else:
                msg.attach(MIMEText(email_data.get('text_content', ''), 'plain', 'utf-8'))

            # Send email
            smtp.sendmail(email_data['sender_email'], email_data['recipient'], msg.as_string())
            smtp.quit()

            # Increment proxy email count
            if self.proxy_manager:
                self.proxy_manager.increment_email_count()

            self.logger.info(f"Thread {thread_id}: Email sent to {email_data['recipient']} using {email_data['sender_email']}")
            return True

        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"Thread {thread_id}: Failed to send email to {email_data['recipient']}: {error_msg}")

            # Check if it's an SMTP authentication error and save failed sender
            if any(keyword in error_msg.lower() for keyword in ['authentication', 'login', 'password', 'username']):
                self._save_failed_sender(email_data['sender_email'], f"SMTP Auth Failed: {error_msg}")
            elif any(keyword in error_msg.lower() for keyword in ['proxy', 'connection', 'timeout']):
                self._save_failed_sender(email_data['sender_email'], f"Proxy/Connection Error: {error_msg}")
            else:
                self._save_failed_sender(email_data['sender_email'], f"Send Failed: {error_msg}")

            return False

    def _save_failed_sender(self, sender, error_message):
        """Save failed sender to a text file with timestamp and error message, avoiding duplicates"""
        import datetime
        import os

        failed_senders_file = f"{home}/failed_senders.txt"
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Create the entry to write
        entry = f"[{timestamp}] {sender} - {error_message}\n"

        # Check if this exact entry already exists to avoid duplicates
        entry_exists = False
        if os.path.exists(failed_senders_file):
            try:
                with open(failed_senders_file, 'r', encoding='utf-8') as f:
                    existing_content = f.read()
                    # Check if sender with similar error already exists in the last hour
                    if sender in existing_content:
                        entry_exists = True
            except Exception:
                pass  # If we can't read the file, just proceed to write

        # Write the entry if it doesn't exist
        if not entry_exists:
            try:
                with open(failed_senders_file, 'a', encoding='utf-8') as f:
                    f.write(entry)
                self.logger.info(f"Saved failed sender: {sender}")
            except Exception as e:
                self.logger.error(f"Failed to save failed sender {sender}: {str(e)}")

    def start_sending(self):
        """
        Start the worker threads for sending emails.
        """
        self.stop_event.clear()
        self.threads = []

        for i in range(self.num_threads):
            thread = threading.Thread(target=self.worker_thread, args=(i,))
            thread.daemon = True
            thread.start()
            self.threads.append(thread)

        self.logger.info(f"Started {self.num_threads} worker threads")

    def stop_sending(self):
        """
        Stop all worker threads.
        """
        self.stop_event.set()

        # Wait for all threads to finish
        for thread in self.threads:
            thread.join(timeout=5)

        self.logger.info("All worker threads stopped")

    def get_statistics(self):
        """
        Get current sending statistics.

        Returns:
            dict: Statistics including emails sent, failed, etc.
        """
        with self.stats_lock:
            return {
                'emails_sent': self.emails_sent,
                'emails_failed': self.emails_failed,
                'queue_size': self.email_queue.qsize(),
                'threads_active': len([t for t in self.threads if t.is_alive()])
            }


class HealthCheckWorker(QObject):
    """Worker class for running health checks in a separate thread"""
    finished = pyqtSignal()
    log_message = pyqtSignal(str)
    progress_update = pyqtSignal(str)

    def __init__(self, senders_file_name, failed_senders_file):
        super().__init__()
        self.senders_file_name = senders_file_name
        self.failed_senders_file = failed_senders_file
        self.cancel = False

    def run(self):
        """Run the health check in a separate thread"""
        # Send initial message
        self.progress_update.emit("Starting sender health check...\n")

        # Import required modules
        import sys
        import os
        import traceback



        # Load senders from file
        try:
            with open(self.senders_file_name, 'r') as senders_file:
                senders = senders_file.readlines()

            # Group senders by ISP
            gmail_senders = []
            yahoo_senders = []
            gmx_senders = []
            other_senders = []

            for sender in senders:
                sender = sender.strip()
                if not sender:
                    continue

                if "@gmail.com" in sender:
                    gmail_senders.append(sender)
                elif "@yahoo.com" in sender:
                    yahoo_senders.append(sender)
                elif "@gmx.com" in sender or "@gmx.de" in sender or "@gmx.us" in sender:
                    gmx_senders.append(sender)
                else:
                    other_senders.append(sender)

            # Test each group
            total_senders = 0
            successful_senders = 0

            def test_sender_group(senders, smtp_host, group_name):
                nonlocal total_senders, successful_senders

                if not senders:
                    self.progress_update.emit(f"No {group_name} senders found.")
                    return

                self.progress_update.emit(f"\nTesting {len(senders)} {group_name} senders...")

                for i, sender_info in enumerate(senders):
                    if self.cancel:
                        self.progress_update.emit("Health check cancelled.")
                        return



                    # Add a small delay between checks to avoid rate limiting
                    # But not for the first sender in each group
                    if i > 0:
                        time.sleep(1)  # 1 second delay between checks

                    total_senders += 1

                    try:
                        # Parse sender info
                        if ';' in sender_info:
                            sender, passwd = sender_info.split(';')
                        elif ':' in sender_info:
                            sender, passwd = sender_info.split(':')
                        else:
                            self.progress_update.emit(f"❌ Error: Invalid format for {sender_info}")
                            continue

                        # Test SMTP login with retry logic
                        import smtplib
                        import ssl
                        import time
                        import socket

                        # Increase timeout and add retry logic
                        max_retries = 2
                        timeout_value = 30  # Increased from 10 to 30 seconds

                        for retry in range(max_retries + 1):
                            if self.cancel:
                                return

                            try:
                                context = ssl.create_default_context()
                                with smtplib.SMTP(smtp_host, port=587, timeout=timeout_value) as smtp:
                                    smtp.starttls(context=context)
                                    smtp.login(sender, passwd)
                                    self.progress_update.emit(f"✅ {sender}: Login successful")
                                    successful_senders += 1
                                    break  # Success, exit retry loop
                            except (smtplib.SMTPServerDisconnected, TimeoutError, ConnectionRefusedError, socket.timeout) as e:
                                # Only retry on timeout/connection errors
                                if retry < max_retries:
                                    self.progress_update.emit(f"⚠️ {sender}: Retry {retry+1}/{max_retries} - {str(e)}")
                                    time.sleep(2)  # Wait before retrying
                                else:
                                    # Last retry failed
                                    raise  # Re-raise to be caught by outer exception handler
                            except Exception as e:
                                # Don't retry other errors
                                raise

                    except Exception as e:
                        error_msg = f"Health check: {str(e)}"
                        self.progress_update.emit(f"❌ {sender}: Login failed - {str(e)}")

                        # Save to failed senders file
                        self.save_failed_sender(sender, error_msg)

            # Test each group with appropriate SMTP server
            test_sender_group(gmail_senders, "smtp.gmail.com", "Gmail")
            test_sender_group(gmx_senders, "mail.gmx.com", "GMX")
            test_sender_group(yahoo_senders, "smtp.mail.yahoo.com", "Yahoo")
            test_sender_group(other_senders, "smtp.office365.com", "Other")

            # Show summary
            self.progress_update.emit(f"\n--- Health Check Complete ---")
            self.progress_update.emit(f"Total senders tested: {total_senders}")
            self.progress_update.emit(f"Successful logins: {successful_senders}")
            self.progress_update.emit(f"Failed logins: {total_senders - successful_senders}")

            if total_senders > 0:
                success_rate = (successful_senders / total_senders) * 100
                self.progress_update.emit(f"Success rate: {success_rate:.1f}%")

        except Exception as e:
            self.progress_update.emit(f"Error during health check: {str(e)}")



        # Signal that we're done
        self.finished.emit()

    def save_failed_sender(self, sender, error_message):
        """Save failed sender to a text file with timestamp and error message, avoiding duplicates"""
        import datetime
        import os

        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        new_entry = f"{timestamp} | {sender} | {error_message}\n"

        try:
            # Check if file exists
            if not os.path.exists(self.failed_senders_file):
                # Create new file with headers
                with open(self.failed_senders_file, 'w', encoding='utf-8') as file:
                    file.write("# Failed senders log\n# Format: Timestamp | Sender | Error Message\n\n")
                    file.write(new_entry)
                return

            # Check for duplicates (same sender and error, ignoring timestamp)
            existing_entries = []
            duplicate_found = False

            with open(self.failed_senders_file, 'r', encoding='utf-8') as file:
                existing_entries = file.readlines()

            for entry in existing_entries:
                if entry.startswith('#') or not entry.strip():
                    continue

                # Extract sender and error from existing entry
                parts = entry.split(' | ', 2)
                if len(parts) >= 3 and parts[1] == sender and parts[2].strip() == error_message:
                    duplicate_found = True
                    break

            # Only add if not a duplicate
            if not duplicate_found:
                with open(self.failed_senders_file, 'a', encoding='utf-8') as file:
                    file.write(new_entry)
        except Exception as e:
            self.progress_update.emit(f"Error saving failed sender: {str(e)}")

    def cancel_check(self):
        """Cancel the health check"""
        self.cancel = True


class Worker(QObject):
    finished = pyqtSignal()
    log_message = pyqtSignal(str)
    show_message_box_signal = pyqtSignal()
    pass_result_to_worker_signal = pyqtSignal(bool)
    cancel_send_signal = pyqtSignal()
    stop_signal = pyqtSignal()
    all_emails_sent_signal = pyqtSignal()



    def __init__(self,messageBox,subject_value, from_value, limit_spin_value,delay_value, html_file_name, groups_file,test_mode_value,isp_value,senders_file_name,rotation_checked, use_proxy=False, emails_per_proxy=5, num_threads=3):
        super().__init__()
        self.subject_value = subject_value
        self.from_value = from_value
        self.limit_spin_value = limit_spin_value
        self.html_file_name = html_file_name
        self.groups_file_name = groups_file
        self.test_mode_value = test_mode_value
        self.isp_value = isp_value
        self.delay_spin = delay_value
        self.senders_file_name = senders_file_name
        self.rotation_checked = rotation_checked
        self.failed_senders_file = f"{home}/failed_senders.txt"
        self.use_proxy = use_proxy
        self.emails_per_proxy = emails_per_proxy
        self.num_threads = num_threads

        # Initialize proxy manager if needed
        self.proxy_manager = None
        if self.use_proxy:
            try:
                self.proxy_manager = ProxyManager(emails_per_proxy=self.emails_per_proxy)
                if self.proxy_manager.load_proxies_from_file(f"{home}/proxies.txt"):
                    self.log_message.emit(f"Proxy manager initialized with {len(self.proxy_manager.proxies)} proxies")
                else:
                    self.log_message.emit("⚠️ No proxies loaded, using direct connection")
                    self.use_proxy = False
            except Exception as e:
                self.log_message.emit(f"⚠️ Failed to initialize proxy manager: {str(e)}")
                self.use_proxy = False

        # Initialize multi-threaded email sender
        self.email_sender = MultiThreadedEmailSender(
            num_threads=self.num_threads,
            proxy_manager=self.proxy_manager if self.use_proxy else None
        )
        self.cancel_send = False
        self.messageBox = messageBox
        self.pass_result_to_worker_signal.connect(self.handle_message_box_result)
        self.cancel_send_signal.connect(self.handle_cancel_request)
        self.stop_signal.connect(self.handle_cancel_request)
        logging.basicConfig(
            filename='app.log',
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        )
        self.logger = logging.getLogger("Worker")



    def handle_message_box_result(self, result):
        if result:
            return
        else:
            self.cancel_send_signal.emit()



    def handle_cancel_request(self):
        self.cancel_send = True

    def save_failed_sender(self, sender, error_message):
        """Save failed sender to a text file with timestamp and error message, avoiding duplicates"""
        import datetime
        import os

        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        new_entry = f"{timestamp} | {sender} | {error_message}\n"

        try:
            # Check if file exists
            if not os.path.exists(self.failed_senders_file):
                # Create new file with headers
                with open(self.failed_senders_file, 'w', encoding='utf-8') as file:
                    file.write("# Failed senders log\n# Format: Timestamp | Sender | Error Message\n\n")
                    file.write(new_entry)
                self.log_message.emit(f"Created new failed senders file: {self.failed_senders_file}")
                return

            # Check for duplicates (same sender and error, ignoring timestamp)
            existing_entries = []
            duplicate_found = False

            with open(self.failed_senders_file, 'r', encoding='utf-8') as file:
                existing_entries = file.readlines()

            for entry in existing_entries:
                if entry.startswith('#') or not entry.strip():
                    continue

                # Extract sender and error from existing entry
                parts = entry.split(' | ', 2)
                if len(parts) >= 3 and parts[1] == sender and parts[2].strip() == error_message:
                    duplicate_found = True
                    break

            # Only add if not a duplicate
            if not duplicate_found:
                with open(self.failed_senders_file, 'a', encoding='utf-8') as file:
                    file.write(new_entry)
                self.log_message.emit(f"Saved failed sender to {self.failed_senders_file}")

        except Exception as e:
            self.log_message.emit(f"Error saving failed sender: {str(e)}")



    def run(self):
        """Run the email sending process using multi-threaded approach with proxy support"""
        try:
            self.log_message.emit("Starting email sending process...")

            # Initialize proxy if needed
            if self.use_proxy and self.proxy_manager:
                self.log_message.emit("Initializing proxy connections...")
                working_proxies = self.proxy_manager.get_working_proxies()
                if working_proxies:
                    self.log_message.emit(f"✅ Found {len(working_proxies)} working proxies")
                else:
                    self.log_message.emit("⚠️ No working proxies found, using direct connection")
                    self.use_proxy = False

            # Start the multi-threaded email sender
            self.email_sender.start_sending()
            self.log_message.emit(f"Started {self.num_threads} worker threads")

            # Load email addresses and senders
            self._load_and_process_emails()

        except Exception as e:
            self.log_message.emit(f"❌ Error in email sending process: {str(e)}")
            import traceback
            self.logger.error(f"Error in run method: {traceback.format_exc()}")
        finally:
            # Clean up
            if hasattr(self, 'email_sender'):
                self.email_sender.stop_sending()
            self.finished.emit()

    def _load_and_process_emails(self):
        """Load email addresses and senders, then process them"""
        gmail_isps = []
        yahoo_isps = []
        other_isps = []
        gmx_isps = []

        # Load email addresses
        data_file = f"{home}/email-test.txt" if self.test_mode_value else f"{home}/{self.groups_file_name}"
        with open(data_file, 'r') as f:
            email_addresses = [addr.strip() for addr in f.readlines() if addr.strip()]

        # Load and categorize senders
        with open(f"{home}/{self.senders_file_name}") as senders_file:
            senders = senders_file.readlines()

        for sender in senders:
            sender = sender.strip()
            if "@gmail.com" in sender:
                gmail_isps.append(sender)
            elif "@yahoo.com" in sender:
                yahoo_isps.append(sender)
            elif "@gmx.com" in sender:
                gmx_isps.append(sender)
            else:
                other_isps.append(sender)

        # Select appropriate senders and SMTP host based on ISP
        if self.isp_value == "Gmail":
            senders_acc = gmail_isps
            smtp_host = "smtp.gmail.com"
            smtp_port = 587
        elif self.isp_value == "Gmx":
            senders_acc = gmx_isps
            smtp_host = "mail.gmx.com"
            smtp_port = 587
        elif self.isp_value == "Yahoo":
            senders_acc = yahoo_isps
            smtp_host = "smtp.mail.yahoo.com"
            smtp_port = 587
        else:
            senders_acc = other_isps
            smtp_host = "smtp.office365.com"
            smtp_port = 587

        # Check if we have enough senders
        if not self.test_mode_value:
            needed_senders_number = int(len(email_addresses) / self.limit_spin_value)
            if needed_senders_number > len(senders_acc):
                self.show_message_box_signal.emit()
                return

        if not senders_acc:
            self.log_message.emit(f"❌ No {self.isp_value} senders found!")
            return

        self.log_message.emit(f"📧 Processing {len(email_addresses)} emails with {len(senders_acc)} senders")

        # Load HTML content
        try:
            with open(f"{home}/{self.html_file_name}", 'r', encoding='utf-8') as html_file:
                html_content = html_file.read()
        except Exception as e:
            self.log_message.emit(f"❌ Failed to load HTML content: {str(e)}")
            return

        # Create email jobs and add them to the queue
        self._create_and_queue_emails(email_addresses, senders_acc, smtp_host, smtp_port, html_content)

        # Monitor the email sending progress
        self._monitor_email_progress()

    def _create_and_queue_emails(self, email_addresses, senders_acc, smtp_host, smtp_port, html_content):
        """Create email jobs and add them to the sending queue"""
        sender_index = 0
        sender_count = 0
        emails_per_sender = self.limit_spin_value

        for to_addr in email_addresses:
            if self.cancel_send:
                break

            # Rotate to next sender after sending the specified number of emails
            if self.rotation_checked and sender_count >= emails_per_sender:
                sender_index = (sender_index + 1) % len(senders_acc)
                sender_count = 0
                self.log_message.emit(f"Rotating to next sender (index {sender_index + 1}/{len(senders_acc)})")

            try:
                sender_info = senders_acc[sender_index]
                # Try to split by semicolon first, then by colon if that fails
                if ';' in sender_info:
                    sender_email, sender_password = sender_info.split(';', 1)
                elif ':' in sender_info:
                    sender_email, sender_password = sender_info.split(':', 1)
                else:
                    raise ValueError("Sender info doesn't contain separator")

                sender_email = sender_email.strip()
                sender_password = sender_password.strip()

                # Create email data for the queue
                email_data = {
                    'recipient': to_addr.strip(),
                    'sender_email': sender_email,
                    'sender_password': sender_password,
                    'subject': self.subject_value,
                    'from_name': self.from_value,
                    'html_content': html_content,
                    'smtp_host': smtp_host,
                    'smtp_port': smtp_port
                }

                # Add to email queue
                self.email_sender.add_email_to_queue(email_data)
                sender_count += 1

            except Exception as e:
                self.log_message.emit(f"❌ Error processing sender {sender_index}: {str(e)}")
                continue

        self.log_message.emit(f"📤 Queued {len(email_addresses)} emails for sending")

    def _monitor_email_progress(self):
        """Monitor the progress of email sending"""
        total_emails = self.email_sender.email_queue.qsize()
        self.log_message.emit(f"🚀 Starting to send {total_emails} emails using {self.num_threads} threads")

        # Monitor progress
        last_stats = None
        while True:
            if self.cancel_send:
                self.email_sender.stop_sending()
                self.log_message.emit("❌ Email sending cancelled by user")
                break

            stats = self.email_sender.get_statistics()

            # Update progress if stats changed
            if stats != last_stats:
                emails_sent = stats['emails_sent']
                emails_failed = stats['emails_failed']
                queue_size = stats['queue_size']
                threads_active = stats['threads_active']

                if emails_sent + emails_failed > 0:
                    self.log_message.emit(f"📊 Progress: {emails_sent} sent, {emails_failed} failed, {queue_size} remaining")

                last_stats = stats

            # Check if all emails are processed
            if stats['queue_size'] == 0 and stats['threads_active'] == 0:
                break

            # Process any results from the result queue
            try:
                while True:
                    result = self.email_sender.result_queue.get_nowait()
                    if result['success']:
                        self.log_message.emit(f"✅ Email sent to {result['email_data']['recipient']} using {result['email_data']['sender_email']}")
                    else:
                        self.log_message.emit(f"❌ Failed to send email to {result['email_data']['recipient']}")
                        # Save failed sender
                        self.save_failed_sender(result['email_data']['sender_email'], "Send failed")
            except queue.Empty:
                pass

            # Small delay to avoid busy waiting
            time.sleep(0.5)

        # Final statistics
        final_stats = self.email_sender.get_statistics()
        self.log_message.emit(f"🏁 Email sending completed: {final_stats['emails_sent']} sent, {final_stats['emails_failed']} failed")

        if final_stats['emails_sent'] > 0:
            self.all_emails_sent_signal.emit()



class MainWindow(QMainWindow):
    return_result_signal = pyqtSignal(int)

    def __init__(self):
        super().__init__()
        uic.loadUi(f"{home}/smtp.ui", self)
        self.html_file_name = "offre.html"
        self.html_file_path = f"{home}/{self.html_file_name}"
        self.senders_file_name = "senders.txt"
        self.groups_file_name = "groups.txt"
        self.groups_test_file_name = "email-test.txt"
        self.saved_data_js = f"{home}/saved_data.json"
        self.failed_senders_file = f"{home}/failed_senders.txt"
        self.messageBox = QMessageBox()
        logging.basicConfig(
            filename='app.log',
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        )
        self.logger = logging.getLogger("App")

        # Initialize proxy settings
        self.proxy_settings = {
            'use_proxy': False,
            'emails_per_proxy': 5,
            'num_threads': 3
        }

        # Check for required dependencies
        self.check_dependencies()

        self.setup_ui()
        self.load_saved_data()
        self.return_result_signal.connect(self.handle_returned_result)

    def check_dependencies(self):
        """Check if all required dependencies are installed"""
        pass  # No dependencies to check after removing VPN functionality

    def setup_ui(self):
        old_style = self.rotation_check.styleSheet()
        new_css = f"""
        QCheckBox::indicator:checked {{
            background-image: url("{home}/img/check.svg");
        }}
        """
        arrows_img = f"""
            QSpinBox::up-arrow, QSpinBox::down-arrow {{
                image: url("{home}/img/up.svg");
            }}
            QSpinBox::down-arrow {{
                image: url("{home}/img/down.svg");
            }}
            QSpinBox::up-arrow:hover {{
                image: url("{home}/img/up.svg");
            }}
            QSpinBox::down-arrow:hover {{
                image: url("{home}/img/down.svg");
            }}
        """
        self.limit_spin.setStyleSheet(self.limit_spin.styleSheet()+arrows_img)
        self.delay_spin.setStyleSheet(self.delay_spin.styleSheet()+arrows_img)
        new_check_img = old_style + new_css
        self.test_mode.setStyleSheet(new_check_img)
        self.only_groups.setStyleSheet(new_check_img)
        self.rotation_check.setStyleSheet(new_check_img)
        edit_icon = QIcon(f"{home}/img/edit.svg")
        self.edit_groups.setIcon(edit_icon)
        self.edit_senders.setIcon(edit_icon)
        self.edit_test_emails.setIcon(edit_icon)
        self.edit_btn.clicked.connect(self.editBtn)
        self.preview_btn.clicked.connect(self.prevBtn)
        self.edit_groups.clicked.connect(partial(self.editfile, "Groups"))
        self.edit_senders.clicked.connect(partial(self.editfile, "Senders"))
        self.edit_test_emails.clicked.connect(partial(self.editfile, "Test"))

        # Add buttons for failed senders and health check
        from PyQt6.QtWidgets import QPushButton

        button_style = """
            QPushButton {
                padding: 5px 10px;
                border: 1px solid #ccc;
                border-radius: 3px;
                background-color: #f5f5f5;
                color: #000;
                font-size: 14px;
                font-weight: 600;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
                border: 1px solid #aaa;
            }
        """

        self.view_failed_btn = QPushButton("View Failed Senders", self)
        self.view_failed_btn.setStyleSheet(button_style)
        self.view_failed_btn.clicked.connect(self.view_failed_senders)

        self.health_check_btn = QPushButton("Health Check", self)
        self.health_check_btn.setStyleSheet(button_style)
        self.health_check_btn.clicked.connect(self.health_check_senders)

        self.proxy_config_btn = QPushButton("Proxy Config", self)
        self.proxy_config_btn.setStyleSheet(button_style)
        self.proxy_config_btn.clicked.connect(self.configure_proxies)

        # Add the buttons to the layout
        self.horizontalLayoutWidget_7.layout().addWidget(self.view_failed_btn)
        self.horizontalLayoutWidget_7.layout().addWidget(self.health_check_btn)
        self.horizontalLayoutWidget_7.layout().addWidget(self.proxy_config_btn)
        with open(f"{home}/{self.senders_file_name}", 'r') as send_file:
            self.senders_number = len(send_file.readlines())
        send_file.close()
        self.senders_num.setText(str(self.senders_number))
        with open(f"{home}/{self.groups_file_name}", 'r') as groups_file:
            self.groups_number = len(groups_file.readlines())
        groups_file.close()
        self.groups_num.setText(str(self.groups_number))
        with open(f"{home}/{self.groups_test_file_name}", 'r') as test_groups_file:
            self.test_groups_number = len(test_groups_file.readlines())
        test_groups_file.close()
        try:
            self.send_btn.clicked.connect(self.send)
        except Exception as e:
            self.logger.error(f"Error while trying to send >> {str(e)}")
            QMessageBox.critical(self,"Error","Please Check Logs")
        self.test_mode.stateChanged.connect(self.test_mode_check)
        self.log_text.setReadOnly(True)
        self.log_text.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOn)
        self.log_text.setPlainText(f"Starting Smtp..." + "\n")


    def getting_started(self):
        if self.senders_number == 0:
            QMessageBox.critical(self,"Error Senders File Empty","Please Add at least One Sender!!")
        if self.groups_number == 0:
            self.log_text.setPlainText(f"Groups File is Empty!!" + "\n")
        if self.test_groups_number == 0:
            self.log_text.setPlainText(f"Test Groups File is Emtpy!!" + "\n")


    def showEvent(self, event):
        super().showEvent(event)
        QTimer.singleShot(0, self.getting_started)



    def show_message_box(self):
        msg_box = QMessageBox()
        msg_box.setWindowTitle("Senders Number is Low")
        msg_box.setText("Senders Number is Low you need more, Do you want to proceed with these numbers?")
        msg_box.setStandardButtons(QMessageBox.StandardButton.Ok | QMessageBox.StandardButton.Cancel)
        result = msg_box.exec()
        self.return_result_signal.emit(result)



    def handle_returned_result(self, result):
        # Check if worker still exists before trying to access it
        if hasattr(self, 'worker') and self.worker is not None:
            try:
                if result == QMessageBox.StandardButton.Ok:
                    self.worker.pass_result_to_worker_signal.emit(True)
                else:
                    self.worker.pass_result_to_worker_signal.emit(False)
            except RuntimeError as e:
                # Worker has been deleted, log the issue but don't crash
                if hasattr(self, 'logger'):
                    self.logger.warning(f"Worker object has been deleted, cannot pass result: {str(e)}")
                else:
                    print(f"Warning: Worker object has been deleted, cannot pass result: {str(e)}")
        else:
            # Worker doesn't exist, log the issue
            if hasattr(self, 'logger'):
                self.logger.warning("Worker object is None or doesn't exist, cannot pass result")
            else:
                print("Warning: Worker object is None or doesn't exist, cannot pass result")

    def cleanup_worker(self):
        """Safely cleanup the worker object and its connections"""
        try:
            if hasattr(self, 'worker') and self.worker is not None:
                # Disconnect all signals to prevent further access attempts
                try:
                    self.worker.log_message.disconnect()
                    self.worker.show_message_box_signal.disconnect()
                    self.worker.all_emails_sent_signal.disconnect()
                    self.worker.pass_result_to_worker_signal.disconnect()
                except (RuntimeError, TypeError):
                    # Signals may already be disconnected or object deleted
                    pass

                # Schedule worker for deletion
                self.worker.deleteLater()
                self.worker = None

                if hasattr(self, 'logger'):
                    self.logger.info("Worker cleanup completed successfully")
        except Exception as e:
            if hasattr(self, 'logger'):
                self.logger.error(f"Error during worker cleanup: {str(e)}")
            else:
                print(f"Error during worker cleanup: {str(e)}")



    def send(self):
        if self.subject_input.text() == "" or self.from_input.text() == "":
            QMessageBox.critical(self,"Error", f"Please Check Your Subject & From")
        else:
            # Use proxy settings from configuration
            use_proxy = self.proxy_settings.get('use_proxy', False)
            emails_per_proxy = self.proxy_settings.get('emails_per_proxy', 5)
            num_threads = self.proxy_settings.get('num_threads', 3)

            # Show sending options dialog for advanced settings
            from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QCheckBox, QLabel, QSpinBox, QPushButton

            options_dialog = QDialog(self)
            options_dialog.setWindowTitle("Sending Options")
            options_dialog.setFixedSize(400, 250)
            layout = QVBoxLayout(options_dialog)

            # Proxy checkbox
            use_proxy_checkbox = QCheckBox("Use proxy rotation", options_dialog)
            use_proxy_checkbox.setChecked(use_proxy)
            layout.addWidget(use_proxy_checkbox)

            # Emails per proxy change
            proxy_change_layout = QHBoxLayout()
            emails_per_proxy_label = QLabel("Emails per proxy:", options_dialog)
            proxy_change_layout.addWidget(emails_per_proxy_label)

            emails_per_proxy_spin = QSpinBox(options_dialog)
            emails_per_proxy_spin.setMinimum(1)
            emails_per_proxy_spin.setMaximum(100)
            emails_per_proxy_spin.setValue(emails_per_proxy)
            proxy_change_layout.addWidget(emails_per_proxy_spin)
            layout.addLayout(proxy_change_layout)

            # Number of threads
            thread_layout = QHBoxLayout()
            thread_label = QLabel("Number of threads:", options_dialog)
            thread_layout.addWidget(thread_label)

            thread_spin = QSpinBox(options_dialog)
            thread_spin.setMinimum(1)
            thread_spin.setMaximum(10)
            thread_spin.setValue(num_threads)
            thread_layout.addWidget(thread_spin)
            layout.addLayout(thread_layout)

            # Buttons
            button_layout = QHBoxLayout()
            ok_button = QPushButton("Start Sending", options_dialog)
            cancel_button = QPushButton("Cancel", options_dialog)
            button_layout.addWidget(ok_button)
            button_layout.addWidget(cancel_button)
            layout.addLayout(button_layout)

            # Connect buttons
            ok_button.clicked.connect(options_dialog.accept)
            cancel_button.clicked.connect(options_dialog.reject)

            # Show dialog
            if options_dialog.exec() == QDialog.DialogCode.Accepted:
                use_proxy = use_proxy_checkbox.isChecked()
                emails_per_proxy = emails_per_proxy_spin.value()
                num_threads = thread_spin.value()

                # Update proxy settings
                self.proxy_settings.update({
                    'use_proxy': use_proxy,
                    'emails_per_proxy': emails_per_proxy,
                    'num_threads': num_threads
                })
            else:
                return  # User cancelled

            self.save_data_to_json()
            self.thread = QThread()
            self.worker = Worker(
                self.messageBox,
                self.subject_input.text(),
                self.from_input.text(),
                self.limit_spin.value(),
                self.delay_spin.value(),
                self.html_file_name,
                self.groups_file_name,
                self.test_mode.isChecked(),
                self.isp_list.currentText(),
                self.senders_file_name,
                self.rotation_check.isChecked(),
                use_proxy,
                emails_per_proxy,
                num_threads
            )
            self.worker.log_message.connect(self.update_log_text)
            self.worker.show_message_box_signal.connect(self.show_message_box)
            self.stop_btn.clicked.connect(self.worker.stop_signal.emit)
            self.worker.all_emails_sent_signal.connect(self.show_all_emails_sent_message)
            self.worker.moveToThread(self.thread)
            self.thread.started.connect(self.worker.run)
            self.worker.finished.connect(self.thread.quit)
            self.worker.finished.connect(self.cleanup_worker)
            self.thread.finished.connect(self.thread.deleteLater)
            try:
                self.thread.start()
            except Exception as e:
                self.logger.error(f"Worker Error >> {str(e)}")
            self.send_btn.setEnabled(False)
            self.thread.finished.connect(
                lambda: self.send_btn.setEnabled(True)
            )



    def update_log_text(self,message):
        self.log_text.setPlainText(self.log_text.toPlainText() + message + "\n")




    def show_all_emails_sent_message(self):
        QMessageBox.information(self, "Sender", "All Emails Sent successfully.")




    def editfile(self,file):
        dialog = QDialog(self)
        dialog.setFixedSize(700, 500)
        dialog.setWindowTitle(f"Edit {file}")
        layout = QVBoxLayout(dialog)
        text_editor = QPlainTextEdit(self)
        layout.addWidget(text_editor)
        if file == "Senders":
            file_name = self.senders_file_name
        elif file == "Groups":
            file_name = self.groups_file_name
        else:
            file_name = self.groups_test_file_name
        file_path = f"{home}/{file_name}"
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                text_content = file.read()
                text_editor.setPlainText(text_content)
        except Exception as e:
            self.logger.error(f"Error while opening {file} >> {str(e)}")
        save_button = QPushButton("Save", dialog)
        layout.addWidget(save_button)
        save_button.clicked.connect(lambda: self.saveEditedFile(text_editor,file_path))
        save_button.clicked.connect(lambda: self.close_window(dialog))
        dialog.exec()


    def test_mode_check(self, state):
        if state == 2:
            self.log_text.setPlainText(self.log_text.toPlainText() + "Entering Test Mode!!" + "\n")
            with open(f"{home}/{self.groups_test_file_name}", 'r') as test_groups_file:
                self.test_groups_number = len(test_groups_file.readlines())
                if self.test_groups_number == 0:
                    QMessageBox.critical(self,"Error", f"Please add at least one Test email!!")
        else:
            self.log_text.setPlainText(self.log_text.toPlainText() + "Entering Normal Mode!!" + "\n")


    def prevBtn(self):
        dialog = QDialog(self)
        dialog.setFixedSize(800, 700)
        dialog.setWindowTitle("HTML Preview")
        layout = QVBoxLayout(dialog)
        web_view = QWebEngineView(dialog)
        layout.addWidget(web_view)
        settings = web_view.settings()
        settings = web_view.page().settings()
        settings.setAttribute(QWebEngineSettings.WebAttribute.LocalContentCanAccessRemoteUrls, True)
        web_view.setContextMenuPolicy(Qt.ContextMenuPolicy.NoContextMenu)
        file_url = QUrl.fromLocalFile(self.html_file_path)
        web_view.load(file_url)
        dialog.exec()



    def editBtn(self):
        dialog = QDialog(self)
        dialog.setFixedSize(800, 700)
        dialog.setWindowTitle("Edit Html")
        layout = QVBoxLayout(dialog)
        html_editor = QPlainTextEdit(self)
        layout.addWidget(html_editor)

        file_path = f"{home}/{self.html_file_name}"
        with open(file_path, 'r', encoding='utf-8') as file:
            html_content = file.read()
            html_editor.setPlainText(html_content)

        save_button = QPushButton("Save", dialog)
        layout.addWidget(save_button)
        save_button.clicked.connect(lambda: self.saveHtmlToFile(html_editor))
        save_button.clicked.connect(lambda: self.close_window(dialog))
        dialog.exec()




    def saveHtmlToFile(self, html_editor):
        edited_html = html_editor.toPlainText()
        try:
            with open(self.html_file_path, 'w', encoding='utf-8') as file:
                file.write(edited_html)
            QMessageBox.information(html_editor, "Save HTML", "HTML content saved to 'offre.html' successfully.")
        except Exception as e:
            QMessageBox.critical(html_editor, "Error", f"Failed to save HTML: {str(e)}")




    def saveEditedFile(self, text_editor,file_path):
        edited_text = text_editor.toPlainText()
        if self.only_groups.isChecked():
            lines = edited_text.split("\n")
            cleaned_lines = [line for line in lines if "@googlegroups.com" in line]
            cleaned_lines = list(dict.fromkeys(cleaned_lines))
            edited_text = "\n".join(cleaned_lines)
        else:
            lines = edited_text.split("\n")
            cleaned_lines = list(dict.fromkeys(lines))
            edited_text = "\n".join(cleaned_lines)

        try:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(edited_text)
            QMessageBox.information(text_editor, "Save File", "File content saved successfully.")
            if "senders" in file_path:
                self.senders_num.setText(str(len(edited_text.split("\n"))))
            else:
                self.groups_num.setText(str(len(edited_text.split("\n"))))
        except Exception as e:
            QMessageBox.critical(text_editor, "Error", f"Failed to save File: {str(e)}")



    def close_window(self, dialog):
        dialog.accept()

    def view_failed_senders(self):
        """Open the failed senders file in a dialog for viewing and editing"""
        from PyQt6.QtWidgets import QHBoxLayout
        dialog = QDialog(self)
        dialog.setFixedSize(800, 600)
        dialog.setWindowTitle("Failed Senders")
        layout = QVBoxLayout(dialog)
        text_editor = QPlainTextEdit(self)
        # Make the text editor read-only since we're not saving changes from it
        text_editor.setReadOnly(True)
        layout.addWidget(text_editor)

        # Check if the file exists, create it if it doesn't
        import os
        if not os.path.exists(self.failed_senders_file):
            with open(self.failed_senders_file, 'w', encoding='utf-8') as file:
                file.write("# Failed senders log\n")
                file.write("# Format: Timestamp | Sender | Error Message\n\n")

        # Load the file content
        try:
            with open(self.failed_senders_file, 'r', encoding='utf-8') as file:
                text_content = file.read()
                text_editor.setPlainText(text_content)
        except Exception as e:
            self.logger.error(f"Error while opening failed senders file >> {str(e)}")
            text_editor.setPlainText(f"Error opening file: {str(e)}")

        # Add buttons
        button_layout = QHBoxLayout()

        export_button = QPushButton("Export Email:Pass", dialog)
        export_button.clicked.connect(lambda: self.export_failed_senders(text_editor.toPlainText()))

        clear_button = QPushButton("Clear", dialog)
        clear_button.clicked.connect(lambda: self.clear_failed_senders_file(text_editor))

        close_button = QPushButton("Close", dialog)
        close_button.clicked.connect(lambda: self.close_window(dialog))

        button_layout.addWidget(export_button)
        button_layout.addWidget(clear_button)
        button_layout.addWidget(close_button)

        layout.addLayout(button_layout)
        dialog.exec()

    def export_failed_senders(self, content):
        """Export failed senders in email:pass format"""
        # Create a progress dialog
        from PyQt6.QtWidgets import QProgressDialog
        progress = QProgressDialog("Exporting failed senders...", "Cancel", 0, 100, self)
        progress.setWindowTitle("Export Progress")
        progress.setWindowModality(Qt.WindowModality.WindowModal)
        progress.setMinimumDuration(0)
        progress.setValue(0)
        progress.show()

        # Create a worker class for the export
        class ExportWorker(QObject):
            finished = pyqtSignal(bool, str, int)  # success, message, count
            progress_update = pyqtSignal(int)

            def __init__(self, content, senders_file_name):
                super().__init__()
                self.content = content
                self.senders_file_name = senders_file_name

            def run(self):
                try:
                    # Extract unique email addresses from the failed senders log
                    unique_emails = set()
                    self.progress_update.emit(10)

                    for line in self.content.split('\n'):
                        if line.startswith('#') or not line.strip():
                            continue

                        parts = line.split(' | ', 2)
                        if len(parts) >= 2:
                            email = parts[1].strip()
                            if '@' in email:
                                unique_emails.add(email)

                    if not unique_emails:
                        self.finished.emit(False, "No email addresses found to export.", 0)
                        return

                    self.progress_update.emit(30)

                    # Get the original sender credentials
                    sender_credentials = {}
                    try:
                        with open(self.senders_file_name, 'r') as senders_file:
                            for line in senders_file:
                                line = line.strip()
                                if not line:
                                    continue

                                # Try to parse with different separators
                                if ';' in line:
                                    email, password = line.split(';', 1)
                                    sender_credentials[email.strip()] = password.strip()
                                elif ':' in line:
                                    email, password = line.split(':', 1)
                                    sender_credentials[email.strip()] = password.strip()
                    except Exception as e:
                        self.finished.emit(False, f"Error reading senders file: {str(e)}", 0)
                        return

                    self.progress_update.emit(60)

                    # Create the export file
                    export_file_path = f"{home}/failed_senders_export.txt"
                    with open(export_file_path, 'w', encoding='utf-8') as export_file:
                        for email in sorted(unique_emails):
                            if email in sender_credentials:
                                export_file.write(f"{email}:{sender_credentials[email]}\n")
                            else:
                                export_file.write(f"{email}:password_not_found\n")

                    self.progress_update.emit(100)

                    success_message = f"Failed senders exported to:\n{export_file_path}\n\nTotal emails exported: {len(unique_emails)}"
                    self.finished.emit(True, success_message, len(unique_emails))

                except Exception as e:
                    self.finished.emit(False, f"Failed to export senders: {str(e)}", 0)

        # Create thread and worker
        self.export_thread = QThread()
        self.export_worker = ExportWorker(content, f"{home}/{self.senders_file_name}")

        # Connect signals
        self.export_worker.progress_update.connect(progress.setValue)
        self.export_worker.finished.connect(
            lambda success, message, count: self.handle_export_finished(success, message, count, progress)
        )
        progress.canceled.connect(self.export_thread.quit)

        # Set up the thread
        self.export_worker.moveToThread(self.export_thread)
        self.export_thread.started.connect(self.export_worker.run)
        self.export_worker.finished.connect(self.export_thread.quit)
        self.export_worker.finished.connect(self.export_worker.deleteLater)
        self.export_thread.finished.connect(self.export_thread.deleteLater)

        # Start the thread
        self.export_thread.start()

    def handle_export_finished(self, success, message, _, progress_dialog):
        """Handle the completion of the export process"""
        progress_dialog.close()

        # The third parameter (count) is not used but kept for compatibility
        if success:
            QMessageBox.information(self, "Export Successful", message)
        else:
            QMessageBox.critical(self, "Export Error", message)

    def clear_failed_senders_file(self, text_editor):
        """Clear the failed senders file and reset it with headers"""
        try:
            # Set the text in the editor
            text_editor.setPlainText("# Failed senders log\n# Format: Timestamp | Sender | Error Message\n\n")

            # Also save to the file
            with open(self.failed_senders_file, 'w', encoding='utf-8') as file:
                file.write("# Failed senders log\n# Format: Timestamp | Sender | Error Message\n\n")

            QMessageBox.information(text_editor, "Clear File", "Failed senders file cleared successfully.")
        except Exception as e:
            QMessageBox.critical(text_editor, "Error", f"Failed to clear file: {str(e)}")

    def health_check_senders(self):
        """Test SMTP login for all senders without sending emails"""
        # Create a dialog to show progress and results
        dialog = QDialog(self)
        dialog.setFixedSize(800, 600)
        dialog.setWindowTitle("Sender Health Check")
        layout = QVBoxLayout(dialog)



        # Create a text area for results
        results_text = QPlainTextEdit(self)
        results_text.setReadOnly(True)
        layout.addWidget(results_text)

        # Add buttons
        button_layout = QHBoxLayout()

        start_button = QPushButton("Start Check", dialog)
        cancel_button = QPushButton("Cancel", dialog)
        cancel_button.setEnabled(False)
        close_button = QPushButton("Close", dialog)
        close_button.clicked.connect(lambda: self.close_window(dialog))

        button_layout.addWidget(start_button)
        button_layout.addWidget(cancel_button)
        button_layout.addWidget(close_button)
        layout.addLayout(button_layout)

        # Show the dialog
        dialog.show()

        # Function to start the health check
        def start_health_check():
            # Clear previous results
            results_text.clear()

            # Disable start button, enable cancel button
            start_button.setEnabled(False)
            cancel_button.setEnabled(True)

            # Create a thread and worker for the health check
            self.health_check_thread = QThread()
            self.health_check_worker = HealthCheckWorker(
                f"{home}/{self.senders_file_name}",
                self.failed_senders_file
            )

            # Connect signals
            self.health_check_worker.progress_update.connect(
                lambda msg: self.update_health_check_results(results_text, msg)
            )
            cancel_button.clicked.connect(self.health_check_worker.cancel_check)

            # Set up the thread
            self.health_check_worker.moveToThread(self.health_check_thread)
            self.health_check_thread.started.connect(self.health_check_worker.run)
            self.health_check_worker.finished.connect(self.health_check_thread.quit)
            self.health_check_worker.finished.connect(self.health_check_worker.deleteLater)
            self.health_check_thread.finished.connect(self.health_check_thread.deleteLater)
            self.health_check_thread.finished.connect(
                lambda: cancel_button.setEnabled(False)
            )
            self.health_check_thread.finished.connect(
                lambda: start_button.setEnabled(True)
            )


            # Start the thread
            self.health_check_thread.start()

        # Connect the start button
        start_button.clicked.connect(start_health_check)

        # Show the dialog (will block until closed)
        dialog.exec()

        # If the thread is still running when the dialog is closed, cancel it
        try:
            if hasattr(self, 'health_check_thread') and self.health_check_thread is not None:
                # Check if thread is actually a QThread object
                if hasattr(self.health_check_thread, 'isRunning') and callable(self.health_check_thread.isRunning):
                    if self.health_check_thread.isRunning():
                        if hasattr(self, 'health_check_worker') and self.health_check_worker is not None:
                            self.health_check_worker.cancel_check()
                        self.health_check_thread.quit()
                        # Wait with timeout to avoid hanging
                        if not self.health_check_thread.wait(3000):  # 3 second timeout
                            self.logger.warning("Health check thread did not terminate within timeout")
                else:
                    self.logger.warning(f"Health check thread object is not a valid QThread: {type(self.health_check_thread)}")
                # Clear the references
                self.health_check_thread = None
                self.health_check_worker = None
        except Exception as e:
            self.logger.error(f"Error cleaning up health check thread: {str(e)}")
            # Ensure references are cleared even if there's an error
            self.health_check_thread = None
            self.health_check_worker = None

    def update_health_check_results(self, text_widget, message):
        """Update the health check results text widget"""
        text_widget.setPlainText(text_widget.toPlainText() + message + "\n")
        # Scroll to the bottom
        text_widget.verticalScrollBar().setValue(text_widget.verticalScrollBar().maximum())

    def configure_proxies(self):
        """Open proxy configuration dialog"""
        from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                                   QTextEdit, QPushButton, QSpinBox, QCheckBox,
                                   QTabWidget, QWidget, QListWidget, QListWidgetItem,
                                   QLineEdit, QComboBox, QMessageBox, QProgressDialog)

        dialog = QDialog(self)
        dialog.setWindowTitle("Proxy Configuration")
        dialog.setFixedSize(600, 500)
        layout = QVBoxLayout(dialog)

        # Create tab widget
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)

        # Proxy List Tab
        proxy_tab = QWidget()
        proxy_layout = QVBoxLayout(proxy_tab)

        # Instructions
        instructions = QLabel("Enter proxy configurations (one per line):\nFormat: protocol://host:port:username:password\nExample: socks5://proxy.example.com:1080:user:pass")
        instructions.setWordWrap(True)
        proxy_layout.addWidget(instructions)

        # Proxy list text area
        proxy_text = QTextEdit()
        proxy_text.setPlaceholderText("socks5://proxy1.example.com:1080:user:pass\nhttp://proxy2.example.com:8080:user:pass")

        # Load existing proxy configuration if it exists
        try:
            with open(f"{home}/proxies.txt", 'r') as f:
                proxy_text.setPlainText(f.read())
        except FileNotFoundError:
            pass

        proxy_layout.addWidget(proxy_text)

        # Proxy buttons
        proxy_buttons = QHBoxLayout()

        load_btn = QPushButton("Load from File")
        save_btn = QPushButton("Save to File")
        test_btn = QPushButton("Test Proxies")

        proxy_buttons.addWidget(load_btn)
        proxy_buttons.addWidget(save_btn)
        proxy_buttons.addWidget(test_btn)
        proxy_layout.addLayout(proxy_buttons)

        tab_widget.addTab(proxy_tab, "Proxy List")

        # Settings Tab
        settings_tab = QWidget()
        settings_layout = QVBoxLayout(settings_tab)

        # Proxy rotation settings
        rotation_group = QVBoxLayout()

        use_proxy_checkbox = QCheckBox("Enable proxy rotation")
        use_proxy_checkbox.setChecked(True)
        rotation_group.addWidget(use_proxy_checkbox)

        emails_per_proxy_layout = QHBoxLayout()
        emails_per_proxy_label = QLabel("Emails per proxy:")
        emails_per_proxy_spin = QSpinBox()
        emails_per_proxy_spin.setMinimum(1)
        emails_per_proxy_spin.setMaximum(100)
        emails_per_proxy_spin.setValue(5)
        emails_per_proxy_layout.addWidget(emails_per_proxy_label)
        emails_per_proxy_layout.addWidget(emails_per_proxy_spin)
        rotation_group.addLayout(emails_per_proxy_layout)

        # Threading settings
        thread_layout = QHBoxLayout()
        thread_label = QLabel("Number of threads:")
        thread_spin = QSpinBox()
        thread_spin.setMinimum(1)
        thread_spin.setMaximum(10)
        thread_spin.setValue(3)
        thread_layout.addWidget(thread_label)
        thread_layout.addWidget(thread_spin)
        rotation_group.addLayout(thread_layout)

        settings_layout.addLayout(rotation_group)
        settings_layout.addStretch()

        tab_widget.addTab(settings_tab, "Settings")

        # Dialog buttons
        button_layout = QHBoxLayout()
        ok_button = QPushButton("OK")
        cancel_button = QPushButton("Cancel")
        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)

        # Button connections
        def save_proxies():
            try:
                with open(f"{home}/proxies.txt", 'w') as f:
                    f.write(proxy_text.toPlainText())
                QMessageBox.information(dialog, "Success", "Proxies saved successfully!")
            except Exception as e:
                QMessageBox.critical(dialog, "Error", f"Failed to save proxies: {str(e)}")

        def load_proxies():
            try:
                from PyQt6.QtWidgets import QFileDialog
                filename, _ = QFileDialog.getOpenFileName(dialog, "Load Proxy File", "", "Text Files (*.txt)")
                if filename:
                    with open(filename, 'r') as f:
                        proxy_text.setPlainText(f.read())
            except Exception as e:
                QMessageBox.critical(dialog, "Error", f"Failed to load proxies: {str(e)}")

        def test_proxies():
            proxy_manager = ProxyManager()
            proxy_lines = proxy_text.toPlainText().strip().split('\n')

            if not proxy_lines or not proxy_lines[0]:
                QMessageBox.warning(dialog, "Warning", "No proxies to test!")
                return

            # Create progress dialog
            progress = QProgressDialog("Testing proxies...", "Cancel", 0, len(proxy_lines), dialog)
            progress.setWindowModality(Qt.WindowModality.WindowModal)
            progress.show()

            working_count = 0
            results = []

            for i, line in enumerate(proxy_lines):
                if progress.wasCanceled():
                    break

                line = line.strip()
                if line and not line.startswith('#'):
                    proxy_config = proxy_manager.parse_proxy_string(line)
                    if proxy_config:
                        if proxy_manager.test_proxy_connection(proxy_config):
                            working_count += 1
                            results.append(f"✓ {proxy_config['host']}:{proxy_config['port']} - Working")
                        else:
                            results.append(f"✗ {proxy_config['host']}:{proxy_config['port']} - Failed")
                    else:
                        results.append(f"✗ Invalid format: {line}")

                progress.setValue(i + 1)

            progress.close()

            # Show results
            result_dialog = QDialog(dialog)
            result_dialog.setWindowTitle("Proxy Test Results")
            result_dialog.setFixedSize(500, 400)
            result_layout = QVBoxLayout(result_dialog)

            result_text = QTextEdit()
            result_text.setReadOnly(True)
            result_text.setPlainText(f"Working proxies: {working_count}/{len([l for l in proxy_lines if l.strip() and not l.startswith('#')])}\n\n" + "\n".join(results))
            result_layout.addWidget(result_text)

            close_btn = QPushButton("Close")
            close_btn.clicked.connect(result_dialog.accept)
            result_layout.addWidget(close_btn)

            result_dialog.exec()

        load_btn.clicked.connect(load_proxies)
        save_btn.clicked.connect(save_proxies)
        test_btn.clicked.connect(test_proxies)
        ok_button.clicked.connect(dialog.accept)
        cancel_button.clicked.connect(dialog.reject)

        # Show dialog and handle result
        if dialog.exec() == QDialog.DialogCode.Accepted:
            # Save proxy configuration
            save_proxies()

            # Save settings to configuration
            try:
                config_data = {
                    'use_proxy': use_proxy_checkbox.isChecked(),
                    'emails_per_proxy': emails_per_proxy_spin.value(),
                    'num_threads': thread_spin.value()
                }

                # Load existing saved data and add proxy settings
                try:
                    with open(self.saved_data_js, 'r') as f:
                        existing_data = json.load(f)
                except FileNotFoundError:
                    existing_data = {}

                existing_data.update(config_data)

                with open(self.saved_data_js, 'w') as f:
                    json.dump(existing_data, f)

                QMessageBox.information(self, "Success", "Proxy configuration saved successfully!")

            except Exception as e:
                QMessageBox.critical(self, "Error", f"Failed to save configuration: {str(e)}")

    def load_saved_data(self):
        try:
            with open(self.saved_data_js, 'r') as file:
                data = json.load(file)
                self.subject_input.setText(data.get('subject', ''))
                self.from_input.setText(data.get('from', ''))
                self.limit_spin.setValue(data.get('limit', 150))
                self.delay_spin.setValue(data.get('delay', 49))
                self.only_groups.setChecked(data.get('only_groups', False))
                self.isp_list.setCurrentText(data.get('isp', 'Gmail'))
                if 'rotation' in data:
                    self.rotation_check.setChecked(data['rotation'])

                # Load proxy settings
                self.proxy_settings = {
                    'use_proxy': data.get('use_proxy', False),
                    'emails_per_proxy': data.get('emails_per_proxy', 5),
                    'num_threads': data.get('num_threads', 3)
                }
        except FileNotFoundError:
            # Set default proxy settings
            self.proxy_settings = {
                'use_proxy': False,
                'emails_per_proxy': 5,
                'num_threads': 3
            }



    def save_data_to_json(self):
        data = {
            'subject': self.subject_input.text(),
            'from': self.from_input.text(),
            'limit': self.limit_spin.value(),
            'delay': self.delay_spin.value(),
            'only_groups': self.only_groups.isChecked(),
            'isp': self.isp_list.currentText(),
            'rotation': self.rotation_check.isChecked(),
        }

        # Add proxy settings if they exist
        if hasattr(self, 'proxy_settings'):
            data.update(self.proxy_settings)

        with open(self.saved_data_js, 'w') as file:
            json.dump(data, file)

    def closeEvent(self, event):
        """Handle application close event - clean up threads"""
        self.logger.info("Application closing - cleaning up threads")

        # Clean up health check thread if it exists
        try:
            if hasattr(self, 'health_check_thread') and self.health_check_thread is not None:
                # Check if thread is actually a QThread object
                if hasattr(self.health_check_thread, 'isRunning') and callable(self.health_check_thread.isRunning):
                    if self.health_check_thread.isRunning():
                        self.logger.info("Stopping health check thread")
                        if hasattr(self, 'health_check_worker') and self.health_check_worker is not None:
                            self.health_check_worker.cancel_check()
                        self.health_check_thread.quit()
                        # Wait with timeout to avoid hanging
                        if not self.health_check_thread.wait(2000):  # 2 second timeout
                            self.logger.warning("Health check thread did not terminate within timeout")
                else:
                    self.logger.warning(f"Health check thread object is not a valid QThread: {type(self.health_check_thread)}")
                self.health_check_thread = None
                self.health_check_worker = None
        except Exception as e:
            self.logger.error(f"Error cleaning up health check thread: {str(e)}")
            # Ensure references are cleared even if there's an error
            self.health_check_thread = None
            self.health_check_worker = None

        # Clean up email sending thread if it exists
        try:
            if hasattr(self, 'thread') and self.thread is not None:
                # Check if thread is actually a QThread object
                if hasattr(self.thread, 'isRunning') and callable(self.thread.isRunning):
                    if self.thread.isRunning():
                        self.logger.info("Stopping email sending thread")
                        if hasattr(self, 'worker') and self.worker is not None:
                            self.worker.stop_signal.emit()
                        self.thread.quit()
                        # Wait with timeout to avoid hanging
                        if not self.thread.wait(2000):  # 2 second timeout
                            self.logger.warning("Email sending thread did not terminate within timeout")
                else:
                    self.logger.warning(f"Thread object is not a valid QThread: {type(self.thread)}")

                # Use our cleanup method for proper worker cleanup
                self.cleanup_worker()
                self.thread = None
        except Exception as e:
            self.logger.error(f"Error cleaning up email sending thread: {str(e)}")
            # Ensure references are cleared even if there's an error
            try:
                self.cleanup_worker()
            except:
                pass  # cleanup_worker handles its own exceptions
            self.thread = None

        # Clean up export thread if it exists
        try:
            if hasattr(self, 'export_thread') and self.export_thread is not None:
                # Check if thread is actually a QThread object
                if hasattr(self.export_thread, 'isRunning') and callable(self.export_thread.isRunning):
                    if self.export_thread.isRunning():
                        self.logger.info("Stopping export thread")
                        self.export_thread.quit()
                        # Wait with timeout to avoid hanging
                        if not self.export_thread.wait(2000):  # 2 second timeout
                            self.logger.warning("Export thread did not terminate within timeout")
                else:
                    self.logger.warning(f"Export thread object is not a valid QThread: {type(self.export_thread)}")
                self.export_thread = None
                self.export_worker = None
        except Exception as e:
            self.logger.error(f"Error cleaning up export thread: {str(e)}")
            # Ensure references are cleared even if there's an error
            self.export_thread = None
            self.export_worker = None

        # Save data before closing
        try:
            self.save_data_to_json()
        except Exception as e:
            self.logger.error(f"Error saving data: {str(e)}")

        # Accept the close event
        event.accept()


def initialize_com():
    """Initialize COM for the main thread"""
    try:
        import pythoncom
        pythoncom.CoInitialize()
        print("COM initialized for main thread")
        return True
    except ImportError:
        print("pythoncom not available")
        return False
    except Exception as e:
        print(f"Failed to initialize COM library ({str(e)})")
        return False

if __name__ == "__main__":
    # Initialize COM before creating the QApplication
    initialize_com()

    print("Starting>>>")

    # Check if pywinauto is installed
    try:
        import pywinauto
    except ImportError:
        # Show a message box about missing dependencies
        from PyQt6.QtWidgets import QApplication, QMessageBox
        app = QApplication(sys.argv)
        msg = QMessageBox()
        msg.setIcon(QMessageBox.Icon.Warning)
        msg.setWindowTitle("Missing Dependencies")
        msg.setText("Some required dependencies are missing.")
        msg.setInformativeText("Would you like to run the dependency checker to install them?")
        msg.setStandardButtons(QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

        if msg.exec() == QMessageBox.StandardButton.Yes:
            # Run the dependency checker
            try:
                import subprocess
                import os

                # Get the path to the dependency checker
                script_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                checker_path = os.path.join(script_dir, "check_dependencies.py")

                # Run the checker
                subprocess.Popen([sys.executable, checker_path])
            except Exception as e:
                error_msg = QMessageBox()
                error_msg.setIcon(QMessageBox.Icon.Critical)
                error_msg.setWindowTitle("Error")
                error_msg.setText(f"Failed to run dependency checker: {str(e)}")
                error_msg.exec()

        sys.exit(1)

    # All dependencies are installed, start the application
    app = QApplication(sys.argv)
    window = MainWindow()
    #window.setFixedSize(602, 478)
    app_icon = QIcon()
    app_icon.addFile(f'{home}/img/Groups_16.png', QtCore.QSize(16, 16))
    app_icon.addFile(f'{home}/img/Groups_24.png', QtCore.QSize(24, 24))
    app_icon.addFile(f'{home}/img/Groups_32.png', QtCore.QSize(32, 32))
    app_icon.addFile(f'{home}/img/Groups_48.png', QtCore.QSize(48, 48))
    app_icon.addFile(f'{home}/img/Groups_256.png', QtCore.QSize(256, 256))
    window.setWindowIcon(app_icon)
    window.show()
    app.exec()