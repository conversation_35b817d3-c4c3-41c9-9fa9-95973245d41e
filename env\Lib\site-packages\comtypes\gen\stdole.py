from enum import IntFlag

import comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 as __wrapper_module__
from comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 import (
    I<PERSON><PERSON><PERSON><PERSON>AN<PERSON>, OLE_OPTEXCLUSIVE, <PERSON><PERSON><PERSON><PERSON>AM<PERSON>, OLE_CANCELBOOL,
    OLE_YPOS_PIXELS, Monochrome, FONTNAME, FONTBOLD, IFont,
    <PERSON>LE_XPOS_HIMETRIC, StdFont, FONTSTRIKETHROUGH, DISPMETHOD,
    EXCEPINFO, OLE_XPOS_PIXELS, Unchecked, _check_version, OLE_HANDLE,
    O<PERSON>_COLOR, IFontEventsDisp, Default, OLE_YPOS_HIMETRIC, HRESULT,
    Library, CoClass, BSTR, StdPicture, FontEvents, Checked,
    OLE_XSIZE_HIMETRIC, Picture, dispid, FONTUNDERSCORE, VgaColor,
    IUnknown, DISPPROPERTY, IPictureDisp, <PERSON>ontDisp, Color, IPicture,
    VARIANT_BOOL, <PERSON>LE_<PERSON>NA<PERSON>EDEFAULTBOOL, Font, OLE_XSIZE_CONTAINER,
    typelib_path, OLE_YSIZE_CONTAINER, FONTSIZE, OLE_XPOS_CONTAINER,
    OLE_YSIZE_HIMETRIC, GUID, Gray, OLE_YPOS_CONTAINER, FONTITALIC,
    OLE_YSIZE_PIXELS, COMMETHOD, OLE_XSIZE_PIXELS, IDispatch, _lcid
)


class OLE_TRISTATE(IntFlag):
    Unchecked = 0
    Checked = 1
    Gray = 2


class LoadPictureConstants(IntFlag):
    Default = 0
    Monochrome = 1
    VgaColor = 2
    Color = 4


__all__ = [
    'OLE_OPTEXCLUSIVE', 'OLE_CANCELBOOL', 'OLE_YPOS_PIXELS',
    'Monochrome', 'StdPicture', 'OLE_TRISTATE', 'FontEvents',
    'FONTNAME', 'FONTBOLD', 'IFont', 'OLE_XPOS_HIMETRIC',
    'OLE_XSIZE_HIMETRIC', 'Checked', 'Picture', 'StdFont',
    'FONTUNDERSCORE', 'VgaColor', 'OLE_XSIZE_PIXELS',
    'FONTSTRIKETHROUGH', 'IPictureDisp', 'IFontDisp', 'Color',
    'OLE_XPOS_PIXELS', 'IPicture', 'OLE_ENABLEDEFAULTBOOL',
    'Unchecked', 'LoadPictureConstants', 'Font',
    'OLE_XSIZE_CONTAINER', 'typelib_path', 'OLE_COLOR',
    'OLE_YSIZE_CONTAINER', 'FONTSIZE', 'OLE_XPOS_CONTAINER',
    'IFontEventsDisp', 'OLE_YSIZE_HIMETRIC', 'Default', 'Gray',
    'OLE_YPOS_HIMETRIC', 'OLE_YPOS_CONTAINER', 'FONTITALIC',
    'OLE_YSIZE_PIXELS', 'Library', 'OLE_HANDLE'
]

