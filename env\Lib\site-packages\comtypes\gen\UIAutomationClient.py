from enum import IntFlag

import comtypes.gen._944DE083_8FB8_45CF_BCB7_C477ACB2F897_0_1_0 as __wrapper_module__
from comtypes.gen._944DE083_8FB8_45CF_BCB7_C477ACB2F897_0_1_0 import (
    UIA_DropTarget_DroppedEventId, UIA_AnnotationTypesAttributeId,
    DockPosition_Left, IUIAutomationElement4,
    IUIAutomationProxyFactoryEntry, UIA_FlowsFromPropertyId,
    UIA_LiveRegionChangedEventId, UIA_OutlineColorPropertyId,
    UIA_AccessKeyPropertyId, ZoomUnit_SmallDecrement,
    UIA_ToggleToggleStatePropertyId, ZoomUnit_LargeDecrement,
    UIA_OutlineStylesAttributeId, StyleId_Heading1, tagRECT,
    HeadingLevel8, UIA_PaneControlTypeId,
    UIA_IsRequiredForFormPropertyId, IUIAutomationNotCondition,
    UIA_ScrollHorizontalScrollPercentPropertyId,
    UIA_IsDataValidForFormPropertyId, NotificationProcessing_All,
    IUIAutomation, UIA_RadioButtonControlTypeId,
    IUIAutomationProxyFactory, AnnotationType_EditingLockedChange,
    CUIAutomation, UIA_HelpTextPropertyId, IUIAutomationTextRange2,
    UIA_Invoke_InvokedEventId, IUIAutomationTablePattern,
    StyleId_Heading6,
    UIA_SelectionItem_ElementAddedToSelectionEventId,
    ZoomUnit_NoAmount, IUIAutomationScrollPattern, StyleId_Heading3,
    UIA_RangeValueMaximumPropertyId,
    TextEditChangeType_CompositionFinalized,
    ConnectionRecoveryBehaviorOptions_Disabled,
    UIA_StylesStyleIdPropertyId, UIA_RangeValueValuePropertyId,
    IUIAutomationItemContainerPattern, UIA_CapStyleAttributeId,
    UIA_EditControlTypeId, CoClass, UIA_OrientationPropertyId,
    AnnotationType_DeletionChange,
    UIA_RangeValueSmallChangePropertyId, UIA_ToolTipClosedEventId,
    UIA_VirtualizedItemPatternId, IUIAutomationDropTargetPattern,
    UIA_LegacyIAccessibleHelpPropertyId,
    NotificationProcessing_CurrentThenMostRecent, UIA_ValuePatternId,
    IUIAutomationElement9, ProviderOptions_ServerSideProvider,
    UIA_AnimationStyleAttributeId, UIA_AriaRolePropertyId,
    UIA_HeadingLevelPropertyId,
    UIA_IsLegacyIAccessiblePatternAvailablePropertyId,
    UIA_FormLandmarkTypeId, NavigateDirection_FirstChild,
    ProviderOptions_RefuseNonClientSupport,
    UIA_SpreadsheetItemFormulaPropertyId, UIA_ScrollItemPatternId,
    StyleId_Emphasis, UIA_CheckBoxControlTypeId,
    SynchronizedInputType_KeyUp, AnnotationType_DataValidationError,
    UIA_IsSpreadsheetPatternAvailablePropertyId,
    WindowInteractionState_Running,
    NotificationProcessing_ImportantMostRecent,
    UIA_HeaderControlTypeId, StyleId_BulletedList, COMMETHOD,
    IUIAutomationProxyFactoryMapping, UIA_TextPatternId,
    UIA_WindowControlTypeId, IDispatch, DockPosition_Right,
    UIA_LegacyIAccessiblePatternId, UIA_RotationPropertyId,
    TextUnit_Format, TreeScope_Children, UIA_DataItemControlTypeId,
    StyleId_Heading9, UIA_Drag_DragStartEventId,
    WindowInteractionState_ReadyForUserInteraction,
    UIA_NavigationLandmarkTypeId,
    TreeTraversalOptions_LastToFirstOrder,
    UIA_SelectionIsSelectionRequiredPropertyId,
    IUIAutomationStructureChangedEventHandler, AnnotationType_Author,
    UIA_Transform2ZoomMaximumPropertyId,
    UIA_WindowCanMaximizePropertyId, IUIAutomationWindowPattern,
    AutomationElementMode_Full, UIA_AnnotationDateTimePropertyId,
    ProviderOptions_UseComThreading, AnnotationType_FormatChange,
    UIA_NamePropertyId, UIA_FontWeightAttributeId,
    AnnotationType_UnsyncedChange, IUIAutomation6, StyleId_Heading7,
    UIA_ToolTipControlTypeId, UIA_MarginBottomAttributeId,
    UIA_IsSelectionItemPatternAvailablePropertyId,
    UIA_AppBarControlTypeId, UIA_FillTypePropertyId,
    UIA_AfterParagraphSpacingAttributeId, AnnotationType_Comment,
    IUIAutomationDockPattern,
    UIA_TableItemColumnHeaderItemsPropertyId,
    IUIAutomationTableItemPattern, UIA_Window_WindowClosedEventId,
    UiaChangeInfo, StyleId_Quote, UIA_MenuModeStartEventId,
    UIA_DropTargetDropTargetEffectPropertyId,
    IUIAutomationActiveTextPositionChangedEventHandler,
    UIA_TabsAttributeId, ProviderOptions_ProviderOwnsSetFocus,
    CUIAutomation8, UIA_UnderlineStyleAttributeId, HeadingLevel7,
    UIA_IndentationLeadingAttributeId, UIA_MarginLeadingAttributeId,
    UIA_SpreadsheetPatternId, UIA_LineSpacingAttributeId,
    UIA_LegacyIAccessibleKeyboardShortcutPropertyId,
    UIA_ChangesEventId, UIA_RangeValueMinimumPropertyId,
    WindowVisualState_Maximized, UIA_OverlineColorAttributeId,
    UIA_SelectionCanSelectMultiplePropertyId,
    UIA_DockDockPositionPropertyId, UIA_IsKeyboardFocusablePropertyId,
    UIA_FullDescriptionPropertyId,
    UIA_TableRowOrColumnMajorPropertyId, UIA_OverlineStyleAttributeId,
    UIA_TransformPatternId, TextEditChangeType_Composition,
    IUIAutomationTextRange, TextUnit_Paragraph,
    UIA_ButtonControlTypeId, DockPosition_Bottom,
    UIA_GridRowCountPropertyId, IUIAutomationEventHandler,
    AnnotationType_SpellingError, HeadingLevel_None,
    UIA_GridItemColumnSpanPropertyId, UIA_Drag_DragCompleteEventId,
    UIA_SelectionItem_ElementSelectedEventId, UIA_IsHiddenAttributeId,
    IUIAutomationExpandCollapsePattern, UIA_CustomControlTypeId,
    IUIAutomationCustomNavigationPattern, UIA_FillColorPropertyId,
    UIA_StructureChangedEventId, IUIAutomationGridItemPattern,
    UIA_StylesPatternId, UIA_IsRangeValuePatternAvailablePropertyId,
    AnnotationType_Footnote, ScrollAmount_SmallDecrement,
    UIA_LinkAttributeId, ExpandCollapseState_LeafNode,
    UIA_IsGridPatternAvailablePropertyId,
    UIA_CaretPositionAttributeId, UIA_BackgroundColorAttributeId,
    StyleId_Title, IUIAutomationTreeWalker,
    UIA_GridColumnCountPropertyId,
    UIA_OptimizeForVisualContentPropertyId, UIA_SizePropertyId,
    UIA_SelectionPatternId, IUIAutomationOrCondition,
    ZoomUnit_LargeIncrement, StructureChangeType_ChildrenBulkRemoved,
    UIA_HorizontalTextAlignmentAttributeId, UIA_ToolBarControlTypeId,
    CoalesceEventsOptions_Enabled, UIA_DragIsGrabbedPropertyId,
    RowOrColumnMajor_ColumnMajor, TextUnit_Character,
    NotificationProcessing_ImportantCurrentThenMostRecent,
    UIA_StylesShapePropertyId, UIA_ScrollPatternId,
    IUIAutomationPropertyCondition, TextEditChangeType_None,
    UIA_IsValuePatternAvailablePropertyId, UIA_IsSubscriptAttributeId,
    UIA_WindowCanMinimizePropertyId, SynchronizedInputType_KeyDown,
    TreeScope_Parent, WindowInteractionState_NotResponding,
    UIA_CaretBidiModeAttributeId,
    UIA_Text_TextSelectionChangedEventId,
    UIA_IndentationTrailingAttributeId, AnnotationType_MoveChange,
    IUIAutomationStylesPattern, UIA_ValueIsReadOnlyPropertyId,
    UIA_ScrollHorizontalViewSizePropertyId,
    AnnotationType_Mathematics, UIA_TableColumnHeadersPropertyId,
    UIA_TreeControlTypeId, ZoomUnit_SmallIncrement,
    SynchronizedInputType_RightMouseUp, IUIAutomationInvokePattern,
    UIA_LegacyIAccessibleValuePropertyId, AnnotationType_Sensitive,
    IUIAutomationAndCondition, IUIAutomationBoolCondition,
    UIA_SystemAlertEventId, UIA_IsPeripheralPropertyId,
    IUIAutomationAnnotationPattern, UIA_Window_WindowOpenedEventId,
    DockPosition_Top, HeadingLevel2, WindowVisualState_Minimized,
    UIA_AnnotationTypesPropertyId, UIA_SpreadsheetItemPatternId,
    UIA_ActiveTextPositionChangedEventId,
    UIA_ForegroundColorAttributeId, IUIAutomationTextPattern,
    UIA_SeparatorControlTypeId, StyleId_NumberedList,
    UIA_LegacyIAccessibleDefaultActionPropertyId,
    UIA_VisualEffectsPropertyId, UIA_IsSuperscriptAttributeId,
    UIA_LandmarkTypePropertyId,
    UIA_IsVirtualizedItemPatternAvailablePropertyId,
    UIA_FlowsToPropertyId, IUIAutomationObjectModelPattern,
    UIA_ExpandCollapseExpandCollapseStatePropertyId,
    UIA_DropTarget_DragLeaveEventId, IUIAutomationTextRangeArray,
    UIA_IsControlElementPropertyId,
    StructureChangeType_ChildrenInvalidated,
    ExpandCollapseState_Collapsed, IUIAutomationElement,
    IUIAutomation3, UIA_IsTogglePatternAvailablePropertyId,
    UIA_TableItemPatternId, TreeScope_None,
    UIA_SelectionItemPatternId, ProviderOptions_NonClientAreaProvider,
    WSTRING, UIA_StrikethroughStyleAttributeId,
    UIA_FrameworkIdPropertyId, IUIAutomationGridPattern, Polite,
    UIA_AnnotationTargetPropertyId,
    IUIAutomationFocusChangedEventHandler, UIA_IsDialogPropertyId,
    UIA_Selection2CurrentSelectedItemPropertyId,
    SynchronizedInputType_LeftMouseDown, UIA_TreeItemControlTypeId,
    IUIAutomationElement2, IUIAutomationCondition,
    UIA_IsSelectionPattern2AvailablePropertyId,
    IUIAutomationRangeValuePattern,
    UIA_DropTargetDropTargetEffectsPropertyId,
    UIA_InputDiscardedEventId, UIA_StylesExtendedPropertiesPropertyId,
    UIA_IsDockPatternAvailablePropertyId, StyleId_Normal,
    UIA_ScrollVerticalViewSizePropertyId,
    SynchronizedInputType_RightMouseDown,
    IUIAutomationTextEditPattern,
    UIA_AnnotationAnnotationTypeIdPropertyId,
    IUIAutomationNotificationEventHandler,
    IUIAutomationSynchronizedInputPattern,
    TextPatternRangeEndpoint_Start, UIA_TitleBarControlTypeId,
    UIA_IsTransformPattern2AvailablePropertyId,
    UIA_FontSizeAttributeId, UIA_ProviderDescriptionPropertyId,
    IUIAutomationLegacyIAccessiblePattern, UIA_GridItemPatternId,
    UIA_ListItemControlTypeId, UIA_BeforeParagraphSpacingAttributeId,
    UIA_AutomationPropertyChangedEventId, HeadingLevel5,
    UIA_LocalizedControlTypePropertyId, IUIAutomationElement8,
    HeadingLevel6, ToggleState_On,
    UIA_SpreadsheetItemAnnotationTypesPropertyId,
    UIA_IsItemContainerPatternAvailablePropertyId,
    OrientationType_Horizontal, UIA_IsContentElementPropertyId,
    ToggleState_Off, IUIAutomationSpreadsheetPattern,
    AnnotationType_AdvancedProofingIssue,
    UIA_DragGrabbedItemsPropertyId,
    UIA_WindowWindowInteractionStatePropertyId,
    RowOrColumnMajor_RowMajor,
    UIA_IsGridItemPatternAvailablePropertyId,
    UIA_HasKeyboardFocusPropertyId, UIA_Drag_DragCancelEventId,
    SynchronizedInputType_LeftMouseUp, UIA_StylesStyleNamePropertyId,
    UIA_ScrollVerticallyScrollablePropertyId, typelib_path,
    UIA_SayAsInterpretAsAttributeId, NavigateDirection_NextSibling,
    StructureChangeType_ChildRemoved, UIA_ImageControlTypeId,
    UIA_StylesFillPatternColorPropertyId, UIA_ScrollBarControlTypeId,
    HeadingLevel4, UIA_StylesFillPatternStylePropertyId, tagPOINT,
    UIA_StatusBarControlTypeId, UIA_SummaryChangeId,
    UIA_AcceleratorKeyPropertyId, UIA_IsActiveAttributeId,
    UIA_IsTextPatternAvailablePropertyId,
    PropertyConditionFlags_MatchSubstring, AnnotationType_Unknown,
    UIA_MultipleViewSupportedViewsPropertyId,
    TreeTraversalOptions_Default,
    UIA_IsSelectionPatternAvailablePropertyId, TreeScope_Descendants,
    UIA_IsPasswordPropertyId, IUIAutomationSelectionPattern2,
    UIA_ControlTypePropertyId, StructureChangeType_ChildAdded,
    UIA_IsCustomNavigationPatternAvailablePropertyId,
    AnnotationType_Footer, ToggleState_Indeterminate,
    UIA_Selection_InvalidatedEventId,
    UIA_IsStylesPatternAvailablePropertyId, TextUnit_Document,
    UIA_ProcessIdPropertyId, UIA_ClassNamePropertyId,
    UIA_ScrollHorizontallyScrollablePropertyId,
    UIA_IsTextEditPatternAvailablePropertyId,
    ProviderOptions_ClientSideProvider, IUIAutomationSelectionPattern,
    UIA_ItemTypePropertyId, UIA_IsTablePatternAvailablePropertyId,
    UIA_Transform2ZoomMinimumPropertyId, TreeScope_Subtree,
    UIA_WindowIsModalPropertyId, UIA_ComboBoxControlTypeId,
    UIA_Selection2FirstSelectedItemPropertyId, UIA_CulturePropertyId,
    _check_version, UIA_CalendarControlTypeId,
    UIA_TransformCanResizePropertyId,
    UIA_IsTextChildPatternAvailablePropertyId, Off,
    UIA_SelectionItem_ElementRemovedFromSelectionEventId,
    AutomationElementMode_None, UIA_MenuControlTypeId,
    UIA_RangeValuePatternId,
    WindowInteractionState_BlockedByModalWindow,
    UIA_SelectionActiveEndAttributeId, HRESULT, TextUnit_Line,
    UIA_InvokePatternId, UIA_IsMultipleViewPatternAvailablePropertyId,
    IUIAutomationTogglePattern, UIA_Transform2CanZoomPropertyId,
    UIA_TabItemControlTypeId, UIA_ItemContainerPatternId,
    UIA_HeaderItemControlTypeId, NavigateDirection_Parent,
    IUIAutomationValuePattern, UIA_TogglePatternId, BSTR,
    IUIAutomationVirtualizedItemPattern, TextUnit_Page,
    UIA_AsyncContentLoadedEventId, dispid,
    UIA_SemanticZoomControlTypeId, IUIAutomationSelectionItemPattern,
    IUnknown, IUIAutomation5, NotificationKind_Other,
    IUIAutomationElement6, ExpandCollapseState_Expanded,
    UIA_FontNameAttributeId, UIA_SelectionSelectionPropertyId,
    StructureChangeType_ChildrenBulkAdded,
    ProviderOptions_OverrideProvider, AnnotationType_Header,
    IUIAutomationScrollItemPattern, UIA_SelectionPattern2Id,
    IUIAutomationDragPattern, IUIAutomationTextRange3,
    UIA_SliderControlTypeId, ProviderOptions_HasNativeIAccessible,
    UIA_RuntimeIdPropertyId, OrientationType_None,
    UIA_BulletStyleAttributeId, PropertyConditionFlags_None,
    UIA_StylesFillColorPropertyId, UIA_StrikethroughColorAttributeId,
    UIA_IsOffscreenPropertyId,
    UIA_ScrollVerticalScrollPercentPropertyId,
    ProviderOptions_UseClientCoordinates, UIA_MenuOpenedEventId,
    CoalesceEventsOptions_Disabled,
    UIA_InputReachedOtherElementEventId,
    UIA_IsTextPattern2AvailablePropertyId, UIA_LevelPropertyId,
    UIA_TableRowHeadersPropertyId, UIA_MultipleViewPatternId,
    StyleId_Heading4, IUIAutomationChangesEventHandler, TextUnit_Word,
    DockPosition_Fill, UIA_SelectionItemSelectionContainerPropertyId,
    UIA_ProgressBarControlTypeId, UIA_TransformCanMovePropertyId,
    SupportedTextSelection_Single,
    UIA_MultipleViewCurrentViewPropertyId, UIA_CultureAttributeId,
    UIA_AutomationFocusChangedEventId,
    UIA_TransformCanRotatePropertyId,
    StructureChangeType_ChildrenReordered,
    IUIAutomationPropertyChangedEventHandler,
    IUIAutomationElementArray, UIA_UnderlineColorAttributeId,
    ScrollAmount_LargeDecrement, WindowVisualState_Normal,
    IRawElementProviderSimple, UIA_IsScrollPatternAvailablePropertyId,
    UIA_StyleNameAttributeId, UIA_GroupControlTypeId,
    UIA_AnnotationAnnotationTypeNamePropertyId,
    SupportedTextSelection_None, UIA_IsDragPatternAvailablePropertyId,
    TextEditChangeType_AutoCorrect, UIA_ValueValuePropertyId,
    UIA_GridItemContainingGridPropertyId,
    UIA_CustomNavigationPatternId, UIA_Transform2ZoomLevelPropertyId,
    UIA_IsTableItemPatternAvailablePropertyId,
    UIA_LiveSettingPropertyId, UIA_LegacyIAccessibleStatePropertyId,
    IUIAutomationTransformPattern2, UIA_TabControlTypeId,
    UIA_LocalizedLandmarkTypePropertyId,
    UIA_MarginTrailingAttributeId,
    UIA_LegacyIAccessibleSelectionPropertyId,
    UIA_WindowWindowVisualStatePropertyId, ScrollAmount_NoAmount,
    UIA_ToolTipOpenedEventId,
    UIA_LegacyIAccessibleDescriptionPropertyId,
    TextPatternRangeEndpoint_End, UIA_AnnotationAuthorPropertyId,
    UIA_BoundingRectanglePropertyId, UIA_ExpandCollapsePatternId,
    StyleId_Heading2, IUIAutomationElement7, StyleId_Heading8,
    UIA_LegacyIAccessibleNamePropertyId,
    UIA_NativeWindowHandlePropertyId,
    UIA_LegacyIAccessibleChildIdPropertyId, IUIAutomation2,
    UIA_TextChildPatternId, UIA_ItemStatusPropertyId,
    UIA_SpinnerControlTypeId, VARIANT,
    UIA_IndentationFirstLineAttributeId,
    UIA_IsAnnotationPatternAvailablePropertyId,
    UIA_DropTargetPatternId,
    UIA_IsExpandCollapsePatternAvailablePropertyId,
    UIA_TableControlTypeId, TreeScope_Element,
    UIA_TextEdit_ConversionTargetChangedEventId,
    UIA_TextControlTypeId, NotificationKind_ItemRemoved,
    IUIAutomationTextPattern2,
    ConnectionRecoveryBehaviorOptions_Enabled, StyleId_Custom,
    OrientationType_Vertical, UIA_GridItemRowSpanPropertyId,
    ExpandCollapseState_PartiallyExpanded,
    UIA_TableItemRowHeaderItemsPropertyId, UIA_CenterPointPropertyId,
    UIA_StyleIdAttributeId, IUIAutomationCacheRequest,
    UIA_TablePatternId, UIA_AnnotationObjectsPropertyId,
    IUIAutomationSpreadsheetItemPattern, UIA_WindowPatternId,
    AnnotationType_ExternalChange, IUIAutomationMultipleViewPattern,
    UIA_SplitButtonControlTypeId, UIA_WindowIsTopmostPropertyId,
    UIA_SearchLandmarkTypeId, IUIAutomation4, UIA_MenuModeEndEventId,
    HeadingLevel3, UIA_RangeValueIsReadOnlyPropertyId,
    WindowInteractionState_Closing, StyleId_Heading5,
    UIA_TextFlowDirectionsAttributeId, AnnotationType_FormulaError,
    UIA_DropTarget_DragEnterEventId,
    PropertyConditionFlags_IgnoreCase, UIA_SizeOfSetPropertyId,
    NotificationProcessing_MostRecent, UIA_ControllerForPropertyId,
    NotificationProcessing_ImportantAll, StyleId_Subtitle,
    UIA_InputReachedTargetEventId, TextEditChangeType_AutoComplete,
    UIA_ClickablePointPropertyId, UIA_TransformPattern2Id,
    IUIAutomationElement5, NavigateDirection_PreviousSibling,
    HeadingLevel1, IUIAutomationElement3,
    UIA_DragDropEffectsPropertyId, UIA_SynchronizedInputPatternId,
    UIA_HyperlinkControlTypeId, SupportedTextSelection_Multiple,
    _midlSAFEARRAY, IUIAutomationTransformPattern, GUID,
    UIA_GridPatternId, NotificationKind_ItemAdded,
    UIA_ObjectModelPatternId, UIA_PositionInSetPropertyId,
    NotificationKind_ActionAborted, UIA_DockPatternId,
    UIA_MainLandmarkTypeId, UIA_TextEdit_TextChangedEventId, _lcid,
    IUIAutomationTextChildPattern, Assertive,
    UIA_AriaPropertiesPropertyId, UIA_AutomationIdPropertyId,
    UIA_SayAsInterpretAsMetadataId, UIA_DragDropEffectPropertyId,
    TreeTraversalOptions_PostOrder, UIA_TextPattern2Id,
    UIA_DataGridControlTypeId, ScrollAmount_SmallIncrement,
    UIA_IsScrollItemPatternAvailablePropertyId,
    AnnotationType_Endnote, UIA_NotificationEventId,
    UIA_IsInvokePatternAvailablePropertyId, UIA_CustomLandmarkTypeId,
    UIA_GridItemColumnPropertyId, UIA_Selection2ItemCountPropertyId,
    NotificationKind_ActionCompleted,
    UIA_IsWindowPatternAvailablePropertyId, TreeScope_Ancestors,
    UIA_LayoutInvalidatedEventId, AnnotationType_GrammarError,
    UIA_AnnotationPatternId,
    UIA_IsSpreadsheetItemPatternAvailablePropertyId,
    UIA_LabeledByPropertyId, UIA_RangeValueLargeChangePropertyId,
    UIA_LegacyIAccessibleRolePropertyId, UIA_IsItalicAttributeId,
    UIA_GridItemRowPropertyId,
    UIA_IsObjectModelPatternAvailablePropertyId,
    AnnotationType_Highlighted, UIA_DocumentControlTypeId,
    UIA_AnnotationObjectsAttributeId,
    UIA_SpreadsheetItemAnnotationObjectsPropertyId,
    UIA_ListControlTypeId, UIA_SelectionItemIsSelectedPropertyId,
    Library, UIA_MenuItemControlTypeId, UIA_IsReadOnlyAttributeId,
    UIA_MenuClosedEventId, IAccessible, UIA_MenuBarControlTypeId,
    UIA_MarginTopAttributeId, DockPosition_None,
    UIA_IsSynchronizedInputPatternAvailablePropertyId,
    UIA_IsDropTargetPatternAvailablePropertyId, HeadingLevel9,
    UIA_IsTransformPatternAvailablePropertyId,
    NavigateDirection_LastChild, AnnotationType_ConflictingChange,
    UIA_OutlineThicknessPropertyId,
    IUIAutomationTextEditTextChangedEventHandler, ExtendedProperty,
    ScrollAmount_LargeIncrement, AnnotationType_InsertionChange,
    RowOrColumnMajor_Indeterminate, UIA_DragPatternId,
    IUIAutomationEventHandlerGroup, UIA_TextEditPatternId,
    UIA_Text_TextChangedEventId, UIA_ThumbControlTypeId,
    AnnotationType_CircularReferenceError,
    AnnotationType_TrackChanges,
    UIA_Selection2LastSelectedItemPropertyId,
    UIA_HostedFragmentRootsInvalidatedEventId,
    UIA_DescribedByPropertyId, UIA_IsEnabledPropertyId
)


class ScrollAmount(IntFlag):
    ScrollAmount_LargeDecrement = 0
    ScrollAmount_SmallDecrement = 1
    ScrollAmount_NoAmount = 2
    ScrollAmount_LargeIncrement = 3
    ScrollAmount_SmallIncrement = 4


class PropertyConditionFlags(IntFlag):
    PropertyConditionFlags_None = 0
    PropertyConditionFlags_IgnoreCase = 1
    PropertyConditionFlags_MatchSubstring = 2


class TreeScope(IntFlag):
    TreeScope_None = 0
    TreeScope_Element = 1
    TreeScope_Children = 2
    TreeScope_Descendants = 4
    TreeScope_Parent = 8
    TreeScope_Ancestors = 16
    TreeScope_Subtree = 7


class NavigateDirection(IntFlag):
    NavigateDirection_Parent = 0
    NavigateDirection_NextSibling = 1
    NavigateDirection_PreviousSibling = 2
    NavigateDirection_FirstChild = 3
    NavigateDirection_LastChild = 4


class AutomationElementMode(IntFlag):
    AutomationElementMode_None = 0
    AutomationElementMode_Full = 1


class WindowVisualState(IntFlag):
    WindowVisualState_Normal = 0
    WindowVisualState_Maximized = 1
    WindowVisualState_Minimized = 2


class WindowInteractionState(IntFlag):
    WindowInteractionState_Running = 0
    WindowInteractionState_Closing = 1
    WindowInteractionState_ReadyForUserInteraction = 2
    WindowInteractionState_BlockedByModalWindow = 3
    WindowInteractionState_NotResponding = 4


class TextPatternRangeEndpoint(IntFlag):
    TextPatternRangeEndpoint_Start = 0
    TextPatternRangeEndpoint_End = 1


class TextUnit(IntFlag):
    TextUnit_Character = 0
    TextUnit_Format = 1
    TextUnit_Word = 2
    TextUnit_Line = 3
    TextUnit_Paragraph = 4
    TextUnit_Page = 5
    TextUnit_Document = 6


class StructureChangeType(IntFlag):
    StructureChangeType_ChildAdded = 0
    StructureChangeType_ChildRemoved = 1
    StructureChangeType_ChildrenInvalidated = 2
    StructureChangeType_ChildrenBulkAdded = 3
    StructureChangeType_ChildrenBulkRemoved = 4
    StructureChangeType_ChildrenReordered = 5


class DockPosition(IntFlag):
    DockPosition_Top = 0
    DockPosition_Left = 1
    DockPosition_Bottom = 2
    DockPosition_Right = 3
    DockPosition_Fill = 4
    DockPosition_None = 5


class TextEditChangeType(IntFlag):
    TextEditChangeType_None = 0
    TextEditChangeType_AutoCorrect = 1
    TextEditChangeType_Composition = 2
    TextEditChangeType_CompositionFinalized = 3
    TextEditChangeType_AutoComplete = 4


class ConnectionRecoveryBehaviorOptions(IntFlag):
    ConnectionRecoveryBehaviorOptions_Disabled = 0
    ConnectionRecoveryBehaviorOptions_Enabled = 1


class CoalesceEventsOptions(IntFlag):
    CoalesceEventsOptions_Disabled = 0
    CoalesceEventsOptions_Enabled = 1


class ProviderOptions(IntFlag):
    ProviderOptions_ClientSideProvider = 1
    ProviderOptions_ServerSideProvider = 2
    ProviderOptions_NonClientAreaProvider = 4
    ProviderOptions_OverrideProvider = 8
    ProviderOptions_ProviderOwnsSetFocus = 16
    ProviderOptions_UseComThreading = 32
    ProviderOptions_RefuseNonClientSupport = 64
    ProviderOptions_HasNativeIAccessible = 128
    ProviderOptions_UseClientCoordinates = 256


class NotificationKind(IntFlag):
    NotificationKind_ItemAdded = 0
    NotificationKind_ItemRemoved = 1
    NotificationKind_ActionCompleted = 2
    NotificationKind_ActionAborted = 3
    NotificationKind_Other = 4


class NotificationProcessing(IntFlag):
    NotificationProcessing_ImportantAll = 0
    NotificationProcessing_ImportantMostRecent = 1
    NotificationProcessing_All = 2
    NotificationProcessing_MostRecent = 3
    NotificationProcessing_CurrentThenMostRecent = 4
    NotificationProcessing_ImportantCurrentThenMostRecent = 5


class OrientationType(IntFlag):
    OrientationType_None = 0
    OrientationType_Horizontal = 1
    OrientationType_Vertical = 2


class LiveSetting(IntFlag):
    Off = 0
    Polite = 1
    Assertive = 2


class ZoomUnit(IntFlag):
    ZoomUnit_NoAmount = 0
    ZoomUnit_LargeDecrement = 1
    ZoomUnit_SmallDecrement = 2
    ZoomUnit_LargeIncrement = 3
    ZoomUnit_SmallIncrement = 4


class SupportedTextSelection(IntFlag):
    SupportedTextSelection_None = 0
    SupportedTextSelection_Single = 1
    SupportedTextSelection_Multiple = 2


class TreeTraversalOptions(IntFlag):
    TreeTraversalOptions_Default = 0
    TreeTraversalOptions_PostOrder = 1
    TreeTraversalOptions_LastToFirstOrder = 2


class RowOrColumnMajor(IntFlag):
    RowOrColumnMajor_RowMajor = 0
    RowOrColumnMajor_ColumnMajor = 1
    RowOrColumnMajor_Indeterminate = 2


class ToggleState(IntFlag):
    ToggleState_Off = 0
    ToggleState_On = 1
    ToggleState_Indeterminate = 2


class ExpandCollapseState(IntFlag):
    ExpandCollapseState_Collapsed = 0
    ExpandCollapseState_Expanded = 1
    ExpandCollapseState_PartiallyExpanded = 2
    ExpandCollapseState_LeafNode = 3


class SynchronizedInputType(IntFlag):
    SynchronizedInputType_KeyUp = 1
    SynchronizedInputType_KeyDown = 2
    SynchronizedInputType_LeftMouseUp = 4
    SynchronizedInputType_LeftMouseDown = 8
    SynchronizedInputType_RightMouseUp = 16
    SynchronizedInputType_RightMouseDown = 32


__all__ = [
    'UIA_DropTarget_DroppedEventId', 'UIA_AnnotationTypesAttributeId',
    'DockPosition_Left', 'IUIAutomationElement4',
    'IUIAutomationProxyFactoryEntry', 'UIA_FlowsFromPropertyId',
    'UIA_LiveRegionChangedEventId', 'UIA_OutlineColorPropertyId',
    'UIA_AccessKeyPropertyId', 'ZoomUnit_SmallDecrement',
    'UIA_ToggleToggleStatePropertyId', 'ZoomUnit_LargeDecrement',
    'ConnectionRecoveryBehaviorOptions', 'AutomationElementMode',
    'UIA_OutlineStylesAttributeId', 'StyleId_Heading1',
    'HeadingLevel8', 'UIA_PaneControlTypeId',
    'UIA_IsRequiredForFormPropertyId', 'IUIAutomationNotCondition',
    'UIA_ScrollHorizontalScrollPercentPropertyId',
    'ExpandCollapseState', 'UIA_IsDataValidForFormPropertyId',
    'NotificationProcessing_All', 'IUIAutomation',
    'UIA_RadioButtonControlTypeId', 'IUIAutomationProxyFactory',
    'AnnotationType_EditingLockedChange', 'CUIAutomation',
    'UIA_HelpTextPropertyId', 'IUIAutomationTextRange2',
    'UIA_Invoke_InvokedEventId', 'IUIAutomationTablePattern',
    'StyleId_Heading6',
    'UIA_SelectionItem_ElementAddedToSelectionEventId',
    'ZoomUnit_NoAmount', 'IUIAutomationScrollPattern',
    'StyleId_Heading3', 'UIA_RangeValueMaximumPropertyId',
    'TextEditChangeType_CompositionFinalized',
    'ConnectionRecoveryBehaviorOptions_Disabled',
    'UIA_StylesStyleIdPropertyId', 'UIA_RangeValueValuePropertyId',
    'IUIAutomationItemContainerPattern', 'UIA_CapStyleAttributeId',
    'UIA_EditControlTypeId', 'UIA_OrientationPropertyId',
    'AnnotationType_DeletionChange',
    'UIA_RangeValueSmallChangePropertyId', 'UIA_ToolTipClosedEventId',
    'UIA_VirtualizedItemPatternId', 'IUIAutomationDropTargetPattern',
    'UIA_LegacyIAccessibleHelpPropertyId',
    'NotificationProcessing_CurrentThenMostRecent',
    'UIA_ValuePatternId', 'IUIAutomationElement9',
    'ProviderOptions_ServerSideProvider',
    'UIA_AnimationStyleAttributeId', 'UIA_AriaRolePropertyId',
    'UIA_HeadingLevelPropertyId',
    'UIA_IsLegacyIAccessiblePatternAvailablePropertyId',
    'WindowVisualState', 'UIA_FormLandmarkTypeId',
    'NavigateDirection_FirstChild',
    'ProviderOptions_RefuseNonClientSupport',
    'UIA_SpreadsheetItemFormulaPropertyId', 'UIA_ScrollItemPatternId',
    'StyleId_Emphasis', 'UIA_CheckBoxControlTypeId',
    'SynchronizedInputType_KeyUp',
    'AnnotationType_DataValidationError',
    'UIA_IsSpreadsheetPatternAvailablePropertyId',
    'WindowInteractionState_Running',
    'NotificationProcessing_ImportantMostRecent',
    'UIA_HeaderControlTypeId', 'StyleId_BulletedList',
    'TreeTraversalOptions', 'IUIAutomationProxyFactoryMapping',
    'UIA_TextPatternId', 'UIA_WindowControlTypeId',
    'DockPosition_Right', 'UIA_LegacyIAccessiblePatternId',
    'UIA_RotationPropertyId', 'TextUnit_Format', 'TreeScope_Children',
    'UIA_DataItemControlTypeId', 'StyleId_Heading9',
    'UIA_Drag_DragStartEventId',
    'WindowInteractionState_ReadyForUserInteraction',
    'UIA_NavigationLandmarkTypeId',
    'TreeTraversalOptions_LastToFirstOrder',
    'UIA_SelectionIsSelectionRequiredPropertyId',
    'IUIAutomationStructureChangedEventHandler',
    'AnnotationType_Author', 'UIA_Transform2ZoomMaximumPropertyId',
    'UIA_WindowCanMaximizePropertyId', 'IUIAutomationWindowPattern',
    'AutomationElementMode_Full', 'UIA_AnnotationDateTimePropertyId',
    'ToggleState', 'ProviderOptions_UseComThreading',
    'AnnotationType_FormatChange', 'UIA_NamePropertyId',
    'UIA_FontWeightAttributeId', 'NavigateDirection',
    'AnnotationType_UnsyncedChange', 'IUIAutomation6',
    'StyleId_Heading7', 'UIA_ToolTipControlTypeId',
    'UIA_MarginBottomAttributeId',
    'UIA_IsSelectionItemPatternAvailablePropertyId',
    'UIA_AppBarControlTypeId', 'UIA_FillTypePropertyId',
    'UIA_AfterParagraphSpacingAttributeId', 'AnnotationType_Comment',
    'IUIAutomationDockPattern',
    'UIA_TableItemColumnHeaderItemsPropertyId',
    'IUIAutomationTableItemPattern', 'UIA_Window_WindowClosedEventId',
    'UiaChangeInfo', 'StyleId_Quote', 'UIA_MenuModeStartEventId',
    'UIA_DropTargetDropTargetEffectPropertyId',
    'IUIAutomationActiveTextPositionChangedEventHandler',
    'UIA_TabsAttributeId', 'ProviderOptions_ProviderOwnsSetFocus',
    'ProviderOptions', 'CUIAutomation8',
    'UIA_UnderlineStyleAttributeId', 'HeadingLevel7',
    'UIA_IndentationLeadingAttributeId',
    'UIA_MarginLeadingAttributeId', 'UIA_SpreadsheetPatternId',
    'UIA_LineSpacingAttributeId',
    'UIA_LegacyIAccessibleKeyboardShortcutPropertyId',
    'UIA_ChangesEventId', 'OrientationType',
    'UIA_RangeValueMinimumPropertyId', 'WindowVisualState_Maximized',
    'UIA_OverlineColorAttributeId',
    'UIA_SelectionCanSelectMultiplePropertyId',
    'UIA_DockDockPositionPropertyId',
    'UIA_IsKeyboardFocusablePropertyId', 'PropertyConditionFlags',
    'UIA_FullDescriptionPropertyId',
    'UIA_TableRowOrColumnMajorPropertyId',
    'UIA_OverlineStyleAttributeId', 'UIA_TransformPatternId',
    'TextEditChangeType_Composition', 'IUIAutomationTextRange',
    'TextUnit_Paragraph', 'UIA_ButtonControlTypeId',
    'DockPosition_Bottom', 'UIA_GridRowCountPropertyId',
    'IUIAutomationEventHandler', 'AnnotationType_SpellingError',
    'HeadingLevel_None', 'UIA_GridItemColumnSpanPropertyId',
    'UIA_Drag_DragCompleteEventId',
    'UIA_SelectionItem_ElementSelectedEventId',
    'UIA_IsHiddenAttributeId', 'IUIAutomationExpandCollapsePattern',
    'UIA_CustomControlTypeId', 'IUIAutomationCustomNavigationPattern',
    'UIA_FillColorPropertyId', 'UIA_StructureChangedEventId',
    'IUIAutomationGridItemPattern', 'UIA_StylesPatternId',
    'UIA_IsRangeValuePatternAvailablePropertyId',
    'AnnotationType_Footnote', 'ScrollAmount_SmallDecrement',
    'UIA_LinkAttributeId', 'ExpandCollapseState_LeafNode',
    'UIA_IsGridPatternAvailablePropertyId',
    'UIA_CaretPositionAttributeId', 'UIA_BackgroundColorAttributeId',
    'StyleId_Title', 'IUIAutomationTreeWalker',
    'UIA_GridColumnCountPropertyId',
    'UIA_OptimizeForVisualContentPropertyId', 'UIA_SizePropertyId',
    'UIA_SelectionPatternId', 'IUIAutomationOrCondition',
    'ZoomUnit_LargeIncrement',
    'StructureChangeType_ChildrenBulkRemoved',
    'UIA_HorizontalTextAlignmentAttributeId',
    'UIA_ToolBarControlTypeId', 'CoalesceEventsOptions_Enabled',
    'UIA_DragIsGrabbedPropertyId', 'RowOrColumnMajor_ColumnMajor',
    'TextUnit_Character',
    'NotificationProcessing_ImportantCurrentThenMostRecent',
    'UIA_StylesShapePropertyId', 'UIA_ScrollPatternId',
    'IUIAutomationPropertyCondition', 'TextEditChangeType_None',
    'UIA_IsValuePatternAvailablePropertyId',
    'UIA_IsSubscriptAttributeId', 'UIA_WindowCanMinimizePropertyId',
    'SynchronizedInputType_KeyDown', 'TreeScope_Parent',
    'WindowInteractionState_NotResponding',
    'UIA_CaretBidiModeAttributeId',
    'UIA_Text_TextSelectionChangedEventId',
    'UIA_IndentationTrailingAttributeId', 'AnnotationType_MoveChange',
    'IUIAutomationStylesPattern', 'UIA_ValueIsReadOnlyPropertyId',
    'UIA_ScrollHorizontalViewSizePropertyId',
    'AnnotationType_Mathematics', 'UIA_TableColumnHeadersPropertyId',
    'UIA_TreeControlTypeId', 'TextUnit', 'ZoomUnit_SmallIncrement',
    'SynchronizedInputType_RightMouseUp',
    'IUIAutomationInvokePattern',
    'UIA_LegacyIAccessibleValuePropertyId',
    'AnnotationType_Sensitive', 'IUIAutomationAndCondition',
    'IUIAutomationBoolCondition', 'UIA_SystemAlertEventId',
    'UIA_IsPeripheralPropertyId', 'IUIAutomationAnnotationPattern',
    'UIA_Window_WindowOpenedEventId', 'DockPosition_Top',
    'HeadingLevel2', 'WindowVisualState_Minimized',
    'UIA_AnnotationTypesPropertyId', 'UIA_SpreadsheetItemPatternId',
    'UIA_ActiveTextPositionChangedEventId',
    'UIA_ForegroundColorAttributeId', 'IUIAutomationTextPattern',
    'UIA_SeparatorControlTypeId', 'StyleId_NumberedList',
    'UIA_LegacyIAccessibleDefaultActionPropertyId',
    'UIA_VisualEffectsPropertyId', 'UIA_IsSuperscriptAttributeId',
    'UIA_LandmarkTypePropertyId',
    'UIA_IsVirtualizedItemPatternAvailablePropertyId',
    'UIA_FlowsToPropertyId', 'IUIAutomationObjectModelPattern',
    'UIA_ExpandCollapseExpandCollapseStatePropertyId',
    'UIA_DropTarget_DragLeaveEventId', 'IUIAutomationTextRangeArray',
    'UIA_IsControlElementPropertyId',
    'StructureChangeType_ChildrenInvalidated',
    'ExpandCollapseState_Collapsed', 'IUIAutomationElement',
    'LiveSetting', 'IUIAutomation3',
    'UIA_IsTogglePatternAvailablePropertyId',
    'UIA_TableItemPatternId', 'TreeScope_None',
    'UIA_SelectionItemPatternId',
    'ProviderOptions_NonClientAreaProvider',
    'UIA_StrikethroughStyleAttributeId', 'UIA_FrameworkIdPropertyId',
    'IUIAutomationGridPattern', 'Polite',
    'UIA_AnnotationTargetPropertyId',
    'IUIAutomationFocusChangedEventHandler', 'UIA_IsDialogPropertyId',
    'UIA_Selection2CurrentSelectedItemPropertyId',
    'SynchronizedInputType_LeftMouseDown',
    'UIA_TreeItemControlTypeId', 'IUIAutomationElement2',
    'IUIAutomationCondition',
    'UIA_IsSelectionPattern2AvailablePropertyId',
    'IUIAutomationRangeValuePattern',
    'UIA_DropTargetDropTargetEffectsPropertyId',
    'UIA_InputDiscardedEventId',
    'UIA_StylesExtendedPropertiesPropertyId',
    'UIA_IsDockPatternAvailablePropertyId', 'StyleId_Normal',
    'UIA_ScrollVerticalViewSizePropertyId',
    'SynchronizedInputType_RightMouseDown',
    'IUIAutomationTextEditPattern', 'RowOrColumnMajor',
    'UIA_AnnotationAnnotationTypeIdPropertyId',
    'IUIAutomationNotificationEventHandler',
    'IUIAutomationSynchronizedInputPattern',
    'TextPatternRangeEndpoint_Start', 'UIA_TitleBarControlTypeId',
    'UIA_IsTransformPattern2AvailablePropertyId',
    'UIA_FontSizeAttributeId', 'UIA_ProviderDescriptionPropertyId',
    'IUIAutomationLegacyIAccessiblePattern', 'UIA_GridItemPatternId',
    'UIA_ListItemControlTypeId',
    'UIA_BeforeParagraphSpacingAttributeId',
    'UIA_AutomationPropertyChangedEventId', 'HeadingLevel5',
    'UIA_LocalizedControlTypePropertyId', 'IUIAutomationElement8',
    'HeadingLevel6', 'ToggleState_On',
    'UIA_SpreadsheetItemAnnotationTypesPropertyId',
    'UIA_IsItemContainerPatternAvailablePropertyId',
    'OrientationType_Horizontal', 'UIA_IsContentElementPropertyId',
    'ToggleState_Off', 'IUIAutomationSpreadsheetPattern',
    'AnnotationType_AdvancedProofingIssue',
    'UIA_DragGrabbedItemsPropertyId',
    'UIA_WindowWindowInteractionStatePropertyId',
    'RowOrColumnMajor_RowMajor',
    'UIA_IsGridItemPatternAvailablePropertyId',
    'UIA_HasKeyboardFocusPropertyId', 'UIA_Drag_DragCancelEventId',
    'SynchronizedInputType_LeftMouseUp',
    'UIA_StylesStyleNamePropertyId',
    'UIA_ScrollVerticallyScrollablePropertyId', 'typelib_path',
    'UIA_SayAsInterpretAsAttributeId',
    'NavigateDirection_NextSibling',
    'StructureChangeType_ChildRemoved', 'UIA_ImageControlTypeId',
    'UIA_StylesFillPatternColorPropertyId',
    'UIA_ScrollBarControlTypeId', 'HeadingLevel4',
    'UIA_StylesFillPatternStylePropertyId',
    'UIA_StatusBarControlTypeId', 'UIA_SummaryChangeId',
    'UIA_AcceleratorKeyPropertyId', 'UIA_IsActiveAttributeId',
    'UIA_IsTextPatternAvailablePropertyId',
    'PropertyConditionFlags_MatchSubstring', 'AnnotationType_Unknown',
    'UIA_MultipleViewSupportedViewsPropertyId',
    'TreeTraversalOptions_Default',
    'UIA_IsSelectionPatternAvailablePropertyId',
    'TreeScope_Descendants', 'UIA_IsPasswordPropertyId',
    'IUIAutomationSelectionPattern2', 'UIA_ControlTypePropertyId',
    'StructureChangeType_ChildAdded',
    'UIA_IsCustomNavigationPatternAvailablePropertyId',
    'AnnotationType_Footer', 'CoalesceEventsOptions',
    'ToggleState_Indeterminate', 'UIA_Selection_InvalidatedEventId',
    'UIA_IsStylesPatternAvailablePropertyId', 'TextUnit_Document',
    'UIA_ProcessIdPropertyId', 'UIA_ClassNamePropertyId',
    'UIA_ScrollHorizontallyScrollablePropertyId',
    'UIA_IsTextEditPatternAvailablePropertyId',
    'ProviderOptions_ClientSideProvider',
    'IUIAutomationSelectionPattern', 'UIA_ItemTypePropertyId',
    'UIA_IsTablePatternAvailablePropertyId',
    'UIA_Transform2ZoomMinimumPropertyId', 'TreeScope_Subtree',
    'UIA_WindowIsModalPropertyId', 'UIA_ComboBoxControlTypeId',
    'UIA_Selection2FirstSelectedItemPropertyId',
    'UIA_CulturePropertyId', 'SupportedTextSelection',
    'UIA_CalendarControlTypeId', 'UIA_TransformCanResizePropertyId',
    'UIA_IsTextChildPatternAvailablePropertyId', 'Off',
    'SynchronizedInputType',
    'UIA_SelectionItem_ElementRemovedFromSelectionEventId',
    'AutomationElementMode_None', 'UIA_MenuControlTypeId',
    'UIA_RangeValuePatternId',
    'WindowInteractionState_BlockedByModalWindow',
    'UIA_SelectionActiveEndAttributeId', 'TextUnit_Line',
    'UIA_InvokePatternId',
    'UIA_IsMultipleViewPatternAvailablePropertyId',
    'IUIAutomationTogglePattern', 'UIA_Transform2CanZoomPropertyId',
    'UIA_TabItemControlTypeId', 'UIA_ItemContainerPatternId',
    'UIA_HeaderItemControlTypeId', 'NavigateDirection_Parent',
    'IUIAutomationValuePattern', 'UIA_TogglePatternId',
    'IUIAutomationVirtualizedItemPattern', 'TextUnit_Page',
    'UIA_AsyncContentLoadedEventId', 'UIA_SemanticZoomControlTypeId',
    'IUIAutomationSelectionItemPattern', 'IUIAutomation5',
    'NotificationKind_Other', 'IUIAutomationElement6',
    'ExpandCollapseState_Expanded', 'UIA_FontNameAttributeId',
    'UIA_SelectionSelectionPropertyId',
    'StructureChangeType_ChildrenBulkAdded',
    'ProviderOptions_OverrideProvider', 'AnnotationType_Header',
    'IUIAutomationScrollItemPattern', 'UIA_SelectionPattern2Id',
    'IUIAutomationDragPattern', 'IUIAutomationTextRange3',
    'UIA_SliderControlTypeId', 'ProviderOptions_HasNativeIAccessible',
    'UIA_RuntimeIdPropertyId', 'OrientationType_None',
    'UIA_BulletStyleAttributeId', 'PropertyConditionFlags_None',
    'UIA_StylesFillColorPropertyId',
    'UIA_StrikethroughColorAttributeId', 'UIA_IsOffscreenPropertyId',
    'UIA_ScrollVerticalScrollPercentPropertyId',
    'ProviderOptions_UseClientCoordinates', 'UIA_MenuOpenedEventId',
    'TextPatternRangeEndpoint', 'CoalesceEventsOptions_Disabled',
    'UIA_InputReachedOtherElementEventId',
    'UIA_IsTextPattern2AvailablePropertyId', 'UIA_LevelPropertyId',
    'UIA_TableRowHeadersPropertyId', 'UIA_MultipleViewPatternId',
    'StyleId_Heading4', 'IUIAutomationChangesEventHandler',
    'TextUnit_Word', 'ZoomUnit', 'DockPosition_Fill',
    'UIA_SelectionItemSelectionContainerPropertyId',
    'UIA_ProgressBarControlTypeId', 'UIA_TransformCanMovePropertyId',
    'SupportedTextSelection_Single',
    'UIA_MultipleViewCurrentViewPropertyId', 'UIA_CultureAttributeId',
    'UIA_AutomationFocusChangedEventId', 'NotificationProcessing',
    'TextEditChangeType', 'StructureChangeType',
    'UIA_TransformCanRotatePropertyId',
    'StructureChangeType_ChildrenReordered',
    'IUIAutomationPropertyChangedEventHandler',
    'IUIAutomationElementArray', 'UIA_UnderlineColorAttributeId',
    'ScrollAmount_LargeDecrement', 'WindowVisualState_Normal',
    'IRawElementProviderSimple',
    'UIA_IsScrollPatternAvailablePropertyId',
    'UIA_StyleNameAttributeId', 'UIA_GroupControlTypeId',
    'UIA_AnnotationAnnotationTypeNamePropertyId',
    'SupportedTextSelection_None',
    'UIA_IsDragPatternAvailablePropertyId',
    'TextEditChangeType_AutoCorrect', 'UIA_ValueValuePropertyId',
    'UIA_GridItemContainingGridPropertyId',
    'UIA_CustomNavigationPatternId',
    'UIA_Transform2ZoomLevelPropertyId',
    'UIA_IsTableItemPatternAvailablePropertyId',
    'UIA_LiveSettingPropertyId',
    'UIA_LegacyIAccessibleStatePropertyId',
    'IUIAutomationTransformPattern2', 'UIA_TabControlTypeId',
    'UIA_LocalizedLandmarkTypePropertyId',
    'UIA_MarginTrailingAttributeId',
    'UIA_LegacyIAccessibleSelectionPropertyId',
    'UIA_WindowWindowVisualStatePropertyId', 'ScrollAmount_NoAmount',
    'UIA_ToolTipOpenedEventId',
    'UIA_LegacyIAccessibleDescriptionPropertyId',
    'TextPatternRangeEndpoint_End', 'UIA_AnnotationAuthorPropertyId',
    'UIA_BoundingRectanglePropertyId', 'UIA_ExpandCollapsePatternId',
    'StyleId_Heading2', 'IUIAutomationElement7', 'StyleId_Heading8',
    'UIA_LegacyIAccessibleNamePropertyId',
    'UIA_NativeWindowHandlePropertyId',
    'UIA_LegacyIAccessibleChildIdPropertyId', 'ScrollAmount',
    'IUIAutomation2', 'UIA_TextChildPatternId',
    'UIA_ItemStatusPropertyId', 'UIA_SpinnerControlTypeId',
    'UIA_IndentationFirstLineAttributeId',
    'UIA_IsAnnotationPatternAvailablePropertyId',
    'UIA_DropTargetPatternId',
    'UIA_IsExpandCollapsePatternAvailablePropertyId',
    'UIA_TableControlTypeId', 'TreeScope_Element',
    'UIA_TextEdit_ConversionTargetChangedEventId',
    'UIA_TextControlTypeId', 'NotificationKind_ItemRemoved',
    'IUIAutomationTextPattern2',
    'ConnectionRecoveryBehaviorOptions_Enabled', 'StyleId_Custom',
    'OrientationType_Vertical', 'UIA_GridItemRowSpanPropertyId',
    'ExpandCollapseState_PartiallyExpanded',
    'UIA_TableItemRowHeaderItemsPropertyId',
    'UIA_CenterPointPropertyId', 'UIA_StyleIdAttributeId',
    'IUIAutomationCacheRequest', 'UIA_TablePatternId',
    'UIA_AnnotationObjectsPropertyId',
    'IUIAutomationSpreadsheetItemPattern', 'UIA_WindowPatternId',
    'AnnotationType_ExternalChange',
    'IUIAutomationMultipleViewPattern',
    'UIA_SplitButtonControlTypeId', 'UIA_WindowIsTopmostPropertyId',
    'UIA_SearchLandmarkTypeId', 'IUIAutomation4',
    'UIA_MenuModeEndEventId', 'HeadingLevel3',
    'UIA_RangeValueIsReadOnlyPropertyId',
    'WindowInteractionState_Closing', 'StyleId_Heading5',
    'UIA_TextFlowDirectionsAttributeId',
    'AnnotationType_FormulaError', 'UIA_DropTarget_DragEnterEventId',
    'PropertyConditionFlags_IgnoreCase', 'UIA_SizeOfSetPropertyId',
    'NotificationProcessing_MostRecent',
    'UIA_ControllerForPropertyId',
    'NotificationProcessing_ImportantAll', 'StyleId_Subtitle',
    'UIA_InputReachedTargetEventId',
    'TextEditChangeType_AutoComplete', 'UIA_ClickablePointPropertyId',
    'UIA_TransformPattern2Id', 'IUIAutomationElement5',
    'NavigateDirection_PreviousSibling', 'HeadingLevel1',
    'IUIAutomationElement3', 'UIA_DragDropEffectsPropertyId',
    'UIA_SynchronizedInputPatternId', 'UIA_HyperlinkControlTypeId',
    'SupportedTextSelection_Multiple', 'DockPosition',
    'IUIAutomationTransformPattern', 'UIA_GridPatternId',
    'NotificationKind_ItemAdded', 'UIA_ObjectModelPatternId',
    'UIA_PositionInSetPropertyId', 'NotificationKind_ActionAborted',
    'UIA_DockPatternId', 'UIA_MainLandmarkTypeId',
    'UIA_TextEdit_TextChangedEventId',
    'IUIAutomationTextChildPattern', 'Assertive',
    'UIA_AriaPropertiesPropertyId', 'UIA_AutomationIdPropertyId',
    'UIA_SayAsInterpretAsMetadataId', 'UIA_DragDropEffectPropertyId',
    'TreeTraversalOptions_PostOrder', 'UIA_TextPattern2Id',
    'UIA_DataGridControlTypeId', 'ScrollAmount_SmallIncrement',
    'UIA_IsScrollItemPatternAvailablePropertyId', 'TreeScope',
    'AnnotationType_Endnote', 'UIA_NotificationEventId',
    'UIA_IsInvokePatternAvailablePropertyId',
    'UIA_CustomLandmarkTypeId', 'UIA_GridItemColumnPropertyId',
    'UIA_Selection2ItemCountPropertyId',
    'NotificationKind_ActionCompleted',
    'UIA_IsWindowPatternAvailablePropertyId', 'TreeScope_Ancestors',
    'UIA_LayoutInvalidatedEventId', 'AnnotationType_GrammarError',
    'UIA_AnnotationPatternId',
    'UIA_IsSpreadsheetItemPatternAvailablePropertyId',
    'UIA_LabeledByPropertyId', 'UIA_RangeValueLargeChangePropertyId',
    'UIA_LegacyIAccessibleRolePropertyId', 'UIA_IsItalicAttributeId',
    'UIA_GridItemRowPropertyId',
    'UIA_IsObjectModelPatternAvailablePropertyId',
    'AnnotationType_Highlighted', 'UIA_DocumentControlTypeId',
    'UIA_AnnotationObjectsAttributeId',
    'UIA_SpreadsheetItemAnnotationObjectsPropertyId',
    'UIA_ListControlTypeId', 'UIA_SelectionItemIsSelectedPropertyId',
    'Library', 'UIA_MenuItemControlTypeId', 'WindowInteractionState',
    'UIA_IsReadOnlyAttributeId', 'UIA_MenuClosedEventId',
    'IAccessible', 'UIA_MenuBarControlTypeId',
    'UIA_MarginTopAttributeId', 'DockPosition_None',
    'UIA_IsSynchronizedInputPatternAvailablePropertyId',
    'UIA_IsDropTargetPatternAvailablePropertyId', 'HeadingLevel9',
    'UIA_IsTransformPatternAvailablePropertyId',
    'NavigateDirection_LastChild', 'AnnotationType_ConflictingChange',
    'UIA_OutlineThicknessPropertyId',
    'IUIAutomationTextEditTextChangedEventHandler',
    'ExtendedProperty', 'ScrollAmount_LargeIncrement',
    'AnnotationType_InsertionChange',
    'RowOrColumnMajor_Indeterminate', 'NotificationKind',
    'UIA_DragPatternId', 'IUIAutomationEventHandlerGroup',
    'UIA_TextEditPatternId', 'UIA_Text_TextChangedEventId',
    'UIA_ThumbControlTypeId', 'AnnotationType_CircularReferenceError',
    'AnnotationType_TrackChanges',
    'UIA_Selection2LastSelectedItemPropertyId',
    'UIA_HostedFragmentRootsInvalidatedEventId',
    'UIA_DescribedByPropertyId', 'UIA_IsEnabledPropertyId'
]

